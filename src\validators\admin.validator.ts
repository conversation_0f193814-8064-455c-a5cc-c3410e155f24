import { celebrate, Joi, Segments } from "celebrate";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
  BULK_OPERATION,
  TIME_PERIOD,
  GROUP_BY,
  EXPORT_FORMAT,
  VALIDATION_CONSTANT,
} from "../helper/constant";

// Following recipe-ms pattern with function wrapper
const bulkTicketOperationValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_ids: Joi.array()
          .items(Joi.number().integer().positive())
          .min(1)
          .required(),
        operation: Joi.string()
          .valid(...Object.values(BULK_OPERATION))
          .required(),
        data: Joi.object()
          .keys({
            assigned_to_user_id: Joi.number().integer().positive().optional(),
            status: Joi.string()
              .valid(...Object.values(TICKET_STATUS))
              .optional(),
            priority: Joi.string()
              .valid(...Object.values(TICKET_PRIORITY))
              .optional(),
            resolution_note: Joi.string()
              .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
              .optional(),
            change_note: Joi.string()
              .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
              .optional(),
          })
          .optional(),
      })
      .unknown(true),
  });

const ticketAnalyticsValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        organization_id: Joi.string().optional(),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        period: Joi.string()
          .valid(...Object.values(TIME_PERIOD))
          .default(TIME_PERIOD.MONTH),
        group_by: Joi.string()
          .valid(...Object.values(GROUP_BY))
          .default(GROUP_BY.DAY),
        ticket_status: Joi.string()
          .valid(...Object.values(TICKET_STATUS))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .optional(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .optional(),
        include_metrics: Joi.boolean().default(true),
        include_trends: Joi.boolean().default(true),
      })
      .unknown(true),
  });

const dashboardValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        organization_id: Joi.string().optional(),
        period: Joi.string()
          .valid(
            ...Object.values(TIME_PERIOD).filter(
              (p) => p !== TIME_PERIOD.CUSTOM
            )
          )
          .default(TIME_PERIOD.MONTH),
        include_charts: Joi.boolean().default(true),
        include_recent_tickets: Joi.boolean().default(true),
        recent_limit: Joi.number().integer().min(1).max(50).default(10),
      })
      .unknown(true),
  });

const getTicketsWithFiltersValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        search: Joi.string().optional(),
        status: Joi.alternatives()
          .try(
            Joi.string().valid(...Object.values(TICKET_STATUS)),
            Joi.array().items(
              Joi.string().valid(...Object.values(TICKET_STATUS))
            )
          )
          .optional(),
        priority: Joi.alternatives()
          .try(
            Joi.string().valid(...Object.values(TICKET_PRIORITY)),
            Joi.array().items(
              Joi.string().valid(...Object.values(TICKET_PRIORITY))
            )
          )
          .optional(),
        module_type: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .optional(),
        issue_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .optional(),
        assigned_to_user_id: Joi.alternatives()
          .try(Joi.string().valid("null"), Joi.number().integer().positive())
          .optional(),
        organization_id: Joi.string().optional(),
        submitter_email: Joi.string().email().optional(),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        overdue: Joi.boolean().optional(),
        unassigned: Joi.boolean().optional(),
        has_rating: Joi.boolean().optional(),
        sort_by: Joi.string().optional(),
        sort_order: Joi.string().valid("ASC", "DESC").optional(),
      })
      .unknown(true),
  });

const agentPerformanceValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        agent_id: Joi.number().integer().positive().optional(),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        period: Joi.string()
          .valid(...Object.values(TIME_PERIOD))
          .default(TIME_PERIOD.MONTH),
        include_ratings: Joi.boolean().default(true),
        include_response_times: Joi.boolean().default(true),
      })
      .unknown(true),
  });

const exportTicketsValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        format: Joi.string()
          .valid(...Object.values(EXPORT_FORMAT))
          .default(EXPORT_FORMAT.CSV),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        ticket_status: Joi.string()
          .valid(...Object.values(TICKET_STATUS))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .optional(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        include_messages: Joi.boolean().default(false),
        include_attachments: Joi.boolean().default(false),
      })
      .unknown(true),
  });

export {
  bulkTicketOperationValidation,
  ticketAnalyticsValidation,
  dashboardValidation,
  getTicketsWithFiltersValidation,
  agentPerformanceValidation,
  exportTicketsValidation,
};

export default {
  bulkTicketOperationValidation,
  ticketAnalyticsValidation,
  dashboardValidation,
  getTicketsWithFiltersValidation,
  agentPerformanceValidation,
  exportTicketsValidation,
};
