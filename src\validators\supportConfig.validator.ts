import { celebrate, Joi, Segments } from "celebrate";
import { CONFIG_TYPE, VALIDATION_CONSTANT } from "../helper/constant";

// Following recipe-ms pattern with function wrapper
const getConfigValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      organization_id: Joi.string().min(1).max(100).required(),
    }),
  });

const upsertConfigValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        support_pin: Joi.string().min(4).max(20).optional(),
        is_active: Joi.boolean().default(true),
        allow_attachments: Joi.boolean().default(true),
        max_attachment_size: Joi.number()
          .integer()
          .min(1024)
          .max(52428800) // 50MB max
          .default(5242880), // 5MB default
        allowed_file_types: Joi.array()
          .items(
            Joi.string().valid(
              "pdf",
              "doc",
              "docx",
              "txt",
              "rtf",
              "png",
              "jpg",
              "jpeg",
              "gif",
              "bmp",
              "webp",
              "xls",
              "xlsx",
              "csv",
              "zip",
              "rar",
              "7z"
            )
          )
          .min(1)
          .default(["pdf", "png", "jpg", "jpeg", "doc", "docx"]),
        auto_assignment_enabled: Joi.boolean().default(false),
        sla_response_time_hours: Joi.number()
          .integer()
          .min(1)
          .max(168) // 1 week max
          .default(24),
        sla_resolution_time_hours: Joi.number()
          .integer()
          .min(1)
          .max(720) // 30 days max
          .default(72),
        // Priority-specific SLA times
        sla_low_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720) // 30 days max
          .default(120),
        sla_medium_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720) // 30 days max
          .default(72),
        sla_high_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720) // 30 days max
          .default(48),
        sla_urgent_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720) // 30 days max
          .default(24),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      organization_id: Joi.string().min(1).max(100).required(),
    }),
  });

const getAllConfigsValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        is_active: Joi.boolean().optional(),
        organization_id: Joi.string().optional(),
        config_type: Joi.string()
          .valid(...Object.values(CONFIG_TYPE))
          .optional(),
        search: Joi.string().max(100).optional(),
      })
      .unknown(true),
  });

const deleteConfigValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      organization_id: Joi.string().min(1).max(100).required(),
    }),
  });

const toggleSupportValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        is_active: Joi.boolean().required(),
        maintenance_message: Joi.string().max(500).optional(),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      organization_id: Joi.string().min(1).max(100).required(),
    }),
  });

const defaultConfigValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        is_active: Joi.boolean().default(true),
        allow_attachments: Joi.boolean().default(true),
        max_attachment_size: Joi.number()
          .integer()
          .min(1024)
          .max(52428800)
          .default(5242880),
        allowed_file_types: Joi.array()
          .items(
            Joi.string().valid(
              "pdf",
              "doc",
              "docx",
              "txt",
              "rtf",
              "png",
              "jpg",
              "jpeg",
              "gif",
              "bmp",
              "webp",
              "xls",
              "xlsx",
              "csv",
              "zip",
              "rar",
              "7z"
            )
          )
          .min(1)
          .default(["pdf", "png", "jpg", "jpeg", "doc", "docx"]),
        auto_assignment_enabled: Joi.boolean().default(false),
        sla_response_time_hours: Joi.number()
          .integer()
          .min(1)
          .max(168)
          .default(24),
        sla_resolution_time_hours: Joi.number()
          .integer()
          .min(1)
          .max(720)
          .default(72),
        // Priority-specific SLA times
        sla_low_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720)
          .default(120),
        sla_medium_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720)
          .default(72),
        sla_high_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720)
          .default(48),
        sla_urgent_priority_hours: Joi.number()
          .integer()
          .min(1)
          .max(720)
          .default(24),
        reset_to_defaults: Joi.boolean().default(false),
        config_keys: Joi.array().items(Joi.string()).optional(),
      })
      .unknown(true),
  });

export {
  getConfigValidation,
  upsertConfigValidation,
  getAllConfigsValidation,
  deleteConfigValidation,
  toggleSupportValidation,
  defaultConfigValidation,
};

export default {
  getConfigValidation,
  upsertConfigValidation,
  getAllConfigsValidation,
  deleteConfigValidation,
  toggleSupportValidation,
  defaultConfigValidation,
};
