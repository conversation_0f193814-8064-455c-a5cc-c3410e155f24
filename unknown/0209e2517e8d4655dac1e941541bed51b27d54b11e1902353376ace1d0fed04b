import { isCelebrateError } from "celebrate";

const HandleErrorMessage = async (err: any, req: any, res: any, next: any) => {
  try {
    if (isCelebrateError(err)) {
      let errorDetails: any;

      // Check different sources of validation errors
      if (err.details.get("body")) {
        errorDetails = err.details.get("body");
      } else if (err.details.get("query")) {
        errorDetails = err.details.get("query");
      } else if (err.details.get("params")) {
        errorDetails = err.details.get("params");
      } else if (err.details.get("headers")) {
        errorDetails = err.details.get("headers");
      }

      if (
        errorDetails &&
        errorDetails.details &&
        errorDetails.details.length > 0
      ) {
        // Get the first error for the main message
        const firstError = errorDetails.details[0];
        const fieldPath = firstError.path ? firstError.path.join(".") : "field";

        // Create user-friendly main message
        let mainMessage = "Validation failed";

        // Customize main message based on error type
        if (firstError.type === "any.required") {
          mainMessage = `${fieldPath} is required`;
        } else if (firstError.type === "string.empty") {
          mainMessage = `${fieldPath} cannot be empty`;
        } else if (firstError.type === "string.max") {
          mainMessage = `${fieldPath} must not exceed ${firstError.context?.limit} characters`;
        } else if (firstError.type === "string.min") {
          mainMessage = `${fieldPath} must be at least ${firstError.context?.limit} characters`;
        } else if (firstError.type === "number.min") {
          mainMessage = `${fieldPath} must be at least ${firstError.context?.limit}`;
        } else if (firstError.type === "number.max") {
          mainMessage = `${fieldPath} must not exceed ${firstError.context?.limit}`;
        } else if (firstError.type === "any.only") {
          // If a custom message is supplied in the Joi schema, prefer that
          const rawMsg = firstError.message?.replace(/"/g, "").trim();
          if (rawMsg && !/must be one of/i.test(rawMsg)) {
            mainMessage = rawMsg;
          } else {
            let validOptions = "valid options";
            if (
              firstError.context?.valids &&
              Array.isArray(firstError.context.valids)
            ) {
              validOptions = firstError.context.valids.join(", ");
            }
            mainMessage = `${fieldPath} must be one of [${validOptions}]`;
          }
        } else if (firstError.type === "string.pattern.base") {
          mainMessage = `${fieldPath} format is invalid`;
        } else if (firstError.type === "number.base") {
          mainMessage = `${fieldPath} must be a valid number`;
        } else if (firstError.type === "boolean.base") {
          mainMessage = `${fieldPath} must be true or false`;
        } else {
          const cleanMessage = firstError.message
            .replace(/"/g, "")
            .replace(/\\/g, "")
            .trim();
          mainMessage = cleanMessage;
        }

        return res.status(400).json({
          status: false,
          message: mainMessage,
        });
      }

      return res.status(400).json({
        status: false,
        message: "Invalid request data",
      });
    }

    next(err);
  } catch (e: any) {
    console.error("Error in validation middleware:", e);
    return res.status(400).json({
      status: false,
      message: "Validation error occurred",
    });
  }
};

export default HandleErrorMessage;
