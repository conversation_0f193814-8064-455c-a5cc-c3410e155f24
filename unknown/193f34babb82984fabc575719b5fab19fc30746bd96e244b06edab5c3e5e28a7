{"name": "backend-support-ticket-ms", "version": "1.0.0", "description": "This README would normally document whatever steps are necessary to get your application up and running.", "main": "index.js", "scripts": {"start:dev": "nodemon", "dev": "ts-node --files ./src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc -p . && cp -r src/locales build/src/locales", "lint": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "prepare": "husky"}, "lint-staged": {"*": "npm run lint"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.705.0", "amqplib": "^0.10.5", "axios": "^1.9.0", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "form-data": "^4.0.3", "handlebars": "^4.7.8", "helmet": "^8.0.0", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "keycloak-connect": "^26.0.7", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.11.5", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "rate-limiter-flexible": "^7.1.1", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-session": "^1.18.0", "@types/i18n": "^0.13.12", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^22.10.2", "@types/node-cron": "^3.0.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "eslint": "^8.57.1", "husky": "^9.1.7", "lint-staged": "^15.4.3", "nodemon": "^3.1.7", "prettier": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}