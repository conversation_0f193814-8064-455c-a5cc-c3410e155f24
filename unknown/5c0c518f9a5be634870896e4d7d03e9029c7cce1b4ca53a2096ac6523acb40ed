import axios from "axios";
import { db } from "../models";
import { logger } from "../utils/logger";

const Item = db.Item;

/**
 * Keycloak Common Functions - Following Auth Microservice Pattern
 * This file contains essential Keycloak integration functions
 *
 * Note: Support PIN validation has been moved to database-based approach
 * for better reliability and performance
 */

/**
 * Get Keycloak Admin Token
 * Same pattern as auth microservice
 */
const getKeycloakAdminToken = async (): Promise<string | null> => {
  try {
    const tokenUrl = `${global.config.KEYCLOAK_SERVER_URL}realms/${global.config.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token`;

    const response = await axios.post(
      tokenUrl,
      new URLSearchParams({
        grant_type: "client_credentials",
        client_id: global.config.KEYCLOAK_CLIENT_ID,
        client_secret: global.config.KEYCLOAK_SECRET_KEY,
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    logger.info("Keycloak admin token obtained successfully");
    return response.data.access_token;
  } catch (error: any) {
    logger.error("Failed to obtain Keycloak admin token", error, {
      tokenUrl: `${global.config.KEYCLOAK_SERVER_URL}realms/${global.config.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token`,
      operation: "getKeycloakAdminToken",
    });
    return null;
  }
};

/**
 * Make HTTP Request
 * Same pattern as auth microservice
 */
const makeRequest = async (
  url: any,
  method: any,
  payload: any = null,
  headers: any = null
) => {
  try {
    const config: any = {
      method,
      url,
      headers: headers,
    };
    if (payload) {
      config.data = payload;
    }

    const response = await axios(config);
    return response;
  } catch (error: any) {
    logger.error("HTTP request failed", error, {
      url,
      method,
      hasPayload: !!payload,
      operation: "makeRequest",
    });

    if (error.response && error.response.status !== 200) {
      return {
        status: false,
        statusCode: error.response.status,
        message: error.response.data.error_description
          ? error.response.data.error_description
          : error.response.data.errorMessage,
        statusText: error.response.statusText,
        field: error.response.data?.field,
      };
    }
  }

  // Ensure a return value for all code paths
  return {
    status: false,
    statusCode: 500,
    message: "Unknown error in makeRequest",
    statusText: "Error",
  };
};

export { getKeycloakAdminToken, makeRequest };
