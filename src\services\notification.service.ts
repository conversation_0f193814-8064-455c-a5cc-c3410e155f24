/**
 * Notification Service using RabbitMQ (following auth-ms pattern)
 * This service publishes email notifications to RabbitMQ queues
 * The actual email sending is handled by a separate email service
 */

import { RABBITMQ_QUEUE, NOTIFICATION_TYPE } from "../helper/constant";
import rabbitmq from "../rabbitmq/rabbitmq";
import { db } from "../models";

// Email templates for support notifications
export const SUPPORT_EMAIL_TEMPLATES = Object.freeze({
  TICKET_CREATED: {
    subject: "Support Ticket Created - #{ticket_number}",
    template: "ticket_created",
  },
  TICKET_STATUS_UPDATED: {
    subject: "Support Ticket Status Updated - #{ticket_number}",
    template: "ticket_status_updated",
  },
  TICKET_ASSIGNED: {
    subject: "Support Ticket Assigned - #{ticket_number}",
    template: "ticket_assigned",
  },
  TICKET_MESSAGE_ADDED: {
    subject: "New Message on Support Ticket - #{ticket_number}",
    template: "ticket_message_added",
  },
  TICKET_RESOLVED: {
    subject: "Support Ticket Resolved - #{ticket_number}",
    template: "ticket_resolved",
  },
});

/**
 * Development-only notification service
 * Logs notifications instead of sending actual emails
 */

interface NotificationData {
  ticket_number: string;
  submitter_email: string;
  submitter_name: string;
  subject: string;
  previous_status?: string;
  new_status?: string;
  resolution_note?: string;
  assigned_to?: string;
  organization_id?: string;
}

/**
 * Create in-app notification
 */
export const createInAppNotification = async (notificationData: {
  notification_content: string;
  notification_subject: string;
  user_ids?: number[];
  role_ids?: number[];
  branch_ids?: number[];
  department_ids?: number[];
  notification_type: string;
  from_user_id: number;
  redirection_type?: string;
  redirection_id?: number;
}): Promise<void> => {
  try {
    const {
      notification_content,
      notification_subject,
      user_ids = [],
      role_ids = [],
      branch_ids = [],
      department_ids = [],
      notification_type,
      from_user_id,
      redirection_type,
      redirection_id,
    } = notificationData;

    // Create notification record (if NotificationMeta model exists)
    // This follows the TTH backend pattern for in-app notifications
    if (db.NotificationMeta) {
      await db.NotificationMeta.create({
        notification_content,
        notification_subject,
        user_id: JSON.stringify(user_ids),
        role_id: JSON.stringify(role_ids),
        branch_id: JSON.stringify(branch_ids),
        department_id: JSON.stringify(department_ids),
        notification_type,
        notification_status: "active",
        from_user_id,
        redirection_type,
        redirection_id,
      });

      console.log(`📱 In-app notification created: ${notification_subject}`);
    }

    // Send push notifications via RabbitMQ if user IDs are provided
    if (user_ids.length > 0) {
      const pushNotificationData = {
        user_ids,
        title: notification_subject,
        body: notification_content,
        data: {
          notification_type,
          redirection_type,
          redirection_id,
        },
      };

      await rabbitmq.publishMessage(
        RABBITMQ_QUEUE.PUSH_NOTIFICATION_SUCCESS,
        pushNotificationData
      );
      console.log(`📱 Push notification queued for ${user_ids.length} users`);
    }
  } catch (error) {
    console.error("Failed to create in-app notification:", error);
    // Don't throw error to avoid breaking the main flow
  }
};

/**
 * Send ticket created notification via RabbitMQ
 */
export const sendTicketCreatedNotification = async (
  data: NotificationData & { user_id?: number; from_user_id?: number }
): Promise<void> => {
  try {
    const emailData = {
      email: data.submitter_email,
      mail_type: "ticket_created",
      ticket_number: data.ticket_number,
      submitter_name: data.submitter_name,
      subject: data.subject,
      organization_id: data.organization_id,
      CURRENT_DATE: new Date().toLocaleDateString(),
      CURRENT_YEAR: new Date().getFullYear(),
      timestamp: new Date().toISOString(),
    };

    // Publish to RabbitMQ email queue (following auth-ms pattern)
    await rabbitmq.publishMessage(RABBITMQ_QUEUE.EMAIL_NOTIFICATION, emailData);

    console.log(
      `📧 Ticket created notification queued for ${data.submitter_email}`
    );

    // Create in-app notification if user_id is provided
    if (data.user_id && data.from_user_id) {
      await createInAppNotification({
        notification_content: `Your support ticket "${data.subject}" has been created successfully. Ticket ID: ${data.ticket_number}`,
        notification_subject: `Support Ticket Created - ${data.ticket_number}`,
        user_ids: [data.user_id],
        notification_type: NOTIFICATION_TYPE.TICKET_CREATED,
        from_user_id: data.from_user_id,
        redirection_type: "support_ticket",
        redirection_id: data.user_id,
      });
    }
  } catch (error) {
    console.error("Failed to queue ticket created notification:", error);
    // Don't throw error to avoid breaking ticket creation
  }
};

/**
 * Send ticket status updated notification via RabbitMQ
 */
export const sendTicketStatusUpdatedNotification = async (
  data: NotificationData & { user_id?: number; from_user_id?: number }
): Promise<void> => {
  try {
    const emailData = {
      email: data.submitter_email,
      mail_type: "ticket_status_updated",
      ticket_number: data.ticket_number,
      previous_status: data.previous_status,
      new_status: data.new_status,
      resolution_note: data.resolution_note,
      submitter_name: data.submitter_name,
      subject: data.subject,
      CURRENT_DATE: new Date().toLocaleDateString(),
      CURRENT_YEAR: new Date().getFullYear(),
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.EMAIL_NOTIFICATION, emailData);

    console.log(
      `📧 Ticket status updated notification queued for ${data.submitter_email}`
    );

    // Create in-app notification if user_id is provided
    if (data.user_id && data.from_user_id) {
      await createInAppNotification({
        notification_content: `Your support ticket "${data.ticket_number}" status has been updated from "${data.previous_status}" to "${data.new_status}".`,
        notification_subject: `Ticket Status Updated - ${data.ticket_number}`,
        user_ids: [data.user_id],
        notification_type: NOTIFICATION_TYPE.TICKET_STATUS_CHANGED,
        from_user_id: data.from_user_id,
        redirection_type: "support_ticket",
        redirection_id: data.user_id,
      });
    }
  } catch (error) {
    console.error("Failed to queue ticket status updated notification:", error);
  }
};

/**
 * Send ticket assigned notification via RabbitMQ
 */
export const sendTicketAssignedNotification = async (
  data: NotificationData & { user_id?: number; from_user_id?: number }
): Promise<void> => {
  try {
    const emailData = {
      email: data.submitter_email,
      mail_type: "ticket_assigned",
      ticket_number: data.ticket_number,
      assigned_to: data.assigned_to,
      submitter_name: data.submitter_name,
      subject: data.subject,
      CURRENT_DATE: new Date().toLocaleDateString(),
      CURRENT_YEAR: new Date().getFullYear(),
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.EMAIL_NOTIFICATION, emailData);

    console.log(
      `📧 Ticket assigned notification queued for ${data.submitter_email}`
    );

    // Create in-app notification if user_id is provided
    if (data.user_id && data.from_user_id) {
      await createInAppNotification({
        notification_content: `Your support ticket "${data.ticket_number}" has been assigned to ${data.assigned_to}. They will be working on your request.`,
        notification_subject: `Ticket Assigned - ${data.ticket_number}`,
        user_ids: [data.user_id],
        notification_type: NOTIFICATION_TYPE.TICKET_ASSIGNED,
        from_user_id: data.from_user_id,
        redirection_type: "support_ticket",
        redirection_id: data.user_id,
      });
    }
  } catch (error) {
    console.error("Failed to queue ticket assigned notification:", error);
  }
};

/**
 * Send ticket resolved notification via RabbitMQ
 */
export const sendTicketResolvedNotification = async (
  data: NotificationData & { user_id?: number; from_user_id?: number }
): Promise<void> => {
  try {
    const emailData = {
      email: data.submitter_email,
      mail_type: "ticket_resolved",
      ticket_number: data.ticket_number,
      resolution_note: data.resolution_note,
      submitter_name: data.submitter_name,
      subject: data.subject,
      CURRENT_DATE: new Date().toLocaleDateString(),
      CURRENT_YEAR: new Date().getFullYear(),
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.EMAIL_NOTIFICATION, emailData);

    console.log(
      `📧 Ticket resolved notification queued for ${data.submitter_email}`
    );

    // Create in-app notification if user_id is provided
    if (data.user_id && data.from_user_id) {
      await createInAppNotification({
        notification_content: `Your support ticket "${data.ticket_number}" has been resolved. ${data.resolution_note ? `Resolution: ${data.resolution_note}` : ""}`,
        notification_subject: `Ticket Resolved - ${data.ticket_number}`,
        user_ids: [data.user_id],
        notification_type: NOTIFICATION_TYPE.TICKET_RESOLVED,
        from_user_id: data.from_user_id,
        redirection_type: "support_ticket",
        redirection_id: data.user_id,
      });
    }
  } catch (error) {
    console.error("Failed to queue ticket resolved notification:", error);
  }
};

/**
 * Send ticket message added notification via RabbitMQ
 */
export const sendTicketMessageAddedNotification = async (
  data: NotificationData & {
    user_id?: number;
    from_user_id?: number;
    message_text?: string;
    message_from?: string;
  }
): Promise<void> => {
  try {
    const emailData = {
      email: data.submitter_email,
      mail_type: "ticket_message_added",
      ticket_number: data.ticket_number,
      submitter_name: data.submitter_name,
      subject: data.subject,
      message_text: data.message_text,
      message_from: data.message_from,
      CURRENT_DATE: new Date().toLocaleDateString(),
      CURRENT_TIME: new Date().toLocaleTimeString(),
      CURRENT_YEAR: new Date().getFullYear(),
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.EMAIL_NOTIFICATION, emailData);

    console.log(
      `📧 Ticket message notification queued for ${data.submitter_email}`
    );

    // Create in-app notification if user_id is provided
    if (data.user_id && data.from_user_id) {
      await createInAppNotification({
        notification_content: `New message added to your ticket "${data.ticket_number}" by ${data.message_from || "Support Team"}`,
        notification_subject: `New Message - ${data.ticket_number}`,
        user_ids: [data.user_id],
        notification_type: NOTIFICATION_TYPE.MESSAGE_ADDED,
        from_user_id: data.from_user_id,
        redirection_type: "support_ticket",
        redirection_id: data.user_id,
      });
    }
  } catch (error) {
    console.error("Failed to queue ticket message notification:", error);
  }
};

/**
 * Generic support ticket notification function (for backward compatibility)
 */
export const sendSupportTicketNotification = async (
  to: string,
  templateName: string,
  data: any
): Promise<void> => {
  try {
    const emailData = {
      to: to,
      subject: `Support Ticket Notification - ${templateName}`,
      template: templateName.toLowerCase(),
      data: {
        ...data,
        recipient_email: to,
      },
      timestamp: new Date().toISOString(),
    };

    // Publish to RabbitMQ email queue (following auth-ms pattern)
    await rabbitmq.publishMessage(RABBITMQ_QUEUE.EMAIL_NOTIFICATION, emailData);

    console.log(
      `📧 Support ticket notification queued: ${templateName} for ${to}`
    );
  } catch (error) {
    console.error("Failed to queue support ticket notification:", error);
    // Don't throw error to avoid breaking ticket operations
  }
};

// Default export for backward compatibility
export default {
  sendTicketCreatedNotification,
  sendTicketStatusUpdatedNotification,
  sendTicketAssignedNotification,
  sendTicketResolvedNotification,
  sendSupportTicketNotification,
};
