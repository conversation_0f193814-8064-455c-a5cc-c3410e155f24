# Support Ticket Notification & Email Implementation

## Overview

This document explains how the notification and email system has been implemented in the support-ticket-ms, following the patterns established in the TTH backend server.

## Architecture

### 1. **Email Notifications**
- **Technology**: RabbitMQ + Handlebars templates
- **Pattern**: Async message queuing for reliable delivery
- **Templates**: HTML email templates with dynamic data injection

### 2. **In-App Notifications**
- **Technology**: Database storage + Push notifications via RabbitMQ
- **Pattern**: Following TTH backend `createNotification` pattern
- **Features**: User targeting, role-based notifications, redirection support

## Implementation Details

### Notification Service (`src/services/notification.service.ts`)

#### Key Functions:

1. **`sendTicketCreatedNotification`**
   - Sends email when ticket is created
   - Creates in-app notification for ticket owner
   - Template: `ticket_created.hbs`

2. **`sendTicketStatusUpdatedNotification`**
   - Sends email when ticket status changes
   - Creates in-app notification with status change details
   - Template: `ticket_status_updated.hbs`

3. **`sendTicketAssignedNotification`**
   - Sends email when ticket is assigned to agent
   - Creates in-app notification with assignee details
   - Template: `ticket_assigned.hbs`

4. **`sendTicketResolvedNotification`**
   - Sends email when ticket is resolved
   - Creates in-app notification with resolution details
   - Template: `ticket_resolved.hbs`

5. **`createInAppNotification`**
   - Creates in-app notifications following TTH pattern
   - Supports user targeting and push notifications
   - Stores in NotificationMeta table (if available)

### Controller Integration

#### Notification Triggers:

1. **Ticket Creation** (`createTicket`)
   ```typescript
   await sendTicketCreatedNotification({
     ticket_number: ticketSlug,
     submitter_email: userDetails.user_email,
     submitter_name: fullName,
     subject: ticket_title,
     organization_id: effectiveOrganizationId,
     user_id: userId,
     from_user_id: userId,
   });
   ```

2. **Status Updates** (`updateTicket`)
   - Triggered when `ticket_status` changes
   - Includes previous and new status information

3. **Ticket Assignment** (`assignTicket`, `updateTicket`)
   - Triggered when ticket is assigned to an agent
   - Includes assignee information

4. **Ticket Resolution** (`resolveTicket`)
   - Triggered when ticket status changes to resolved
   - Includes resolution notes

## Email Templates

Located in `src/templates/email/`:

- `ticket_created.hbs` - New ticket creation
- `ticket_status_updated.hbs` - Status change notifications
- `ticket_assigned.hbs` - Assignment notifications
- `ticket_resolved.hbs` - Resolution notifications

### Template Variables:

#### Common Variables:
- `{{userName}}` - User's full name
- `{{ticketNumber}}` - Ticket slug/number
- `{{subject}}` - Ticket title/subject

#### Status Update Specific:
- `{{previousStatus}}` - Previous ticket status
- `{{newStatus}}` - New ticket status
- `{{statusMessage}}` - Additional status message

#### Assignment Specific:
- `{{assignedTo}}` - Name of assigned agent

#### Resolution Specific:
- `{{resolutionNote}}` - Resolution details

## RabbitMQ Queues

### Email Queues:
- `EMAIL_NOTIFICATION` - Main email queue
- `MAIL_SUCCESS` - Email delivery success
- `MAIL_FAILED` - Email delivery failures

### Notification Queues:
- `PUSH_NOTIFICATION_SUCCESS` - Push notifications
- `SUPPORT_NOTIFICATION` - Support-specific notifications

## Configuration

### Environment Variables:
```env
RABBITMQ_URL=amqp://localhost:5672
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Constants (`src/helper/constant.ts`):
```typescript
export const RABBITMQ_QUEUE = {
  EMAIL_NOTIFICATION: "email_notification",
  PUSH_NOTIFICATION_SUCCESS: "push_notification_success",
  // ... other queues
};

export const NOTIFICATION_TYPE = {
  TICKET_CREATED: "ticket_created",
  TICKET_ASSIGNED: "ticket_assigned",
  TICKET_STATUS_CHANGED: "ticket_status_changed",
  TICKET_RESOLVED: "ticket_resolved",
  // ... other types
};
```

## Error Handling

- All notification functions use try-catch blocks
- Notification failures don't break main ticket operations
- Comprehensive error logging for debugging
- Graceful degradation when notification services are unavailable

## Usage Examples

### Basic Ticket Creation with Notifications:
```typescript
// Create ticket
const ticket = await Ticket.create(ticketData);

// Send notifications (automatically handled in controller)
// - Email to ticket submitter
// - In-app notification to submitter
// - Push notification (if configured)
```

### Status Change with Notifications:
```typescript
// Update ticket status
await ticket.update({ ticket_status: TICKET_STATUS.IN_PROGRESS });

// Notifications sent automatically:
// - Email with status change details
// - In-app notification with status transition
// - Push notification to mobile devices
```

## Testing

### Email Testing:
1. Configure SMTP settings in environment
2. Create test tickets and verify email delivery
3. Check RabbitMQ queues for message processing

### In-App Notification Testing:
1. Verify NotificationMeta table entries
2. Check push notification queue messages
3. Test notification display in frontend

## Monitoring

### Key Metrics:
- Email delivery success/failure rates
- Notification processing times
- Queue message volumes
- User engagement with notifications

### Logging:
- All notification attempts are logged
- Error details captured for failed deliveries
- Performance metrics for queue processing

## Future Enhancements

1. **SMS Notifications**: Add SMS support for critical updates
2. **Email Preferences**: User-configurable notification preferences
3. **Notification Templates**: Admin-configurable email templates
4. **Delivery Tracking**: Read receipts and delivery confirmations
5. **Batch Notifications**: Bulk notification processing for performance
