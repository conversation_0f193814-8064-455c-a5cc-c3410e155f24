{"UNAUTHORIZED_ACCESS": "Unauthorized access", "INVALID_TOKEN": "Invalid or expired token", "USER_NOT_FOUND": "User not found", "AUTHENTICATION_FAILED": "Authentication failed", "INSUFFICIENT_PERMISSIONS": "Insufficient permissions", "AUTHORIZATION_FAILED": "Authorization failed", "VALIDATION_ERROR": "Validation error", "REQUIRED_FIELDS_MISSING": "Required fields are missing", "TICKET_CREATED_SUCCESSFULLY": "Support ticket created successfully", "TICKET_CREATION_FAILED": "Failed to create support ticket", "TICKETS_FETCHED_SUCCESSFULLY": "Tickets retrieved successfully", "TICKETS_FETCH_FAILED": "Failed to retrieve tickets", "TICKET_FETCHED_SUCCESSFULLY": "Ticket retrieved successfully", "TICKET_FETCH_FAILED": "Failed to retrieve ticket", "TICKET_NOT_FOUND": "Ticket not found", "TICKET_UPDATED_SUCCESSFULLY": "Ticket updated successfully", "TICKET_UPDATE_FAILED": "Failed to update ticket", "TICKET_ASSIGNED_SUCCESSFULLY": "Ticket assigned successfully", "TICKET_ASSIGNMENT_FAILED": "Failed to assign ticket", "TICKET_ESCALATED_SUCCESSFULLY": "Ticket escalated successfully", "TICKET_ESCALATION_FAILED": "Failed to escalate ticket", "MESSAGE_SENT_SUCCESSFULLY": "Message sent successfully", "MESSAGE_SEND_FAILED": "Failed to send message", "MESSAGE_ADDED_SUCCESSFULLY": "Message added successfully", "MESSAGE_ADD_FAILED": "Failed to add message", "MESSAGES_FETCHED_SUCCESSFULLY": "Messages fetched successfully", "MESSAGES_FETCH_FAILED": "Failed to fetch messages", "MESSAGE_NOT_FOUND": "Message not found", "MESSAGE_UPDATED_SUCCESSFULLY": "Message updated successfully", "MESSAGE_UPDATE_FAILED": "Failed to update message", "MESSAGE_DELETED_SUCCESSFULLY": "Message deleted successfully", "MESSAGE_DELETE_FAILED": "Failed to delete message", "INTERNAL_NOTE_ADDED_SUCCESSFULLY": "Internal note added successfully", "INTERNAL_NOTE_ADD_FAILED": "Failed to add internal note", "INTERNAL_NOTE_ACCESS_DENIED": "Only agents can create internal notes", "MESSAGE_MARKED_AS_SOLUTION": "Message marked as solution", "MESSAGE_RATING_SAVED": "Message rating saved successfully", "ESCALATION_COMPLETED": "Escalation completed successfully", "ESCALATION_FAILED": "Escalation failed", "NOTIFICATION_SENT": "Notification sent successfully", "NOTIFICATION_FAILED": "Failed to send notification", "RATE_LIMIT_EXCEEDED": "Rate limit exceeded. Please try again later", "FILE_UPLOAD_RATE_LIMIT_EXCEEDED": "File upload limit exceeded. Please try again later", "STRICT_RATE_LIMIT_EXCEEDED": "Rate limit exceeded for this sensitive operation", "INVALID_FILE_TYPE": "Invalid file type. Please upload a supported file format", "FILE_TOO_LARGE": "File size exceeds the maximum allowed limit", "FILE_UPLOAD_FAILED": "File upload failed", "FILE_UPLOAD_SUCCESSFUL": "File uploaded successfully", "SEARCH_COMPLETED": "Search completed successfully", "SEARCH_FAILED": "Search operation failed", "ANALYTICS_FETCHED": "Analytics data retrieved successfully", "ANALYTICS_FETCH_FAILED": "Failed to fetch analytics data", "CONFIG_UPDATED": "Configuration updated successfully", "CONFIG_UPDATE_FAILED": "Failed to update configuration", "CONFIG_FETCHED": "Configuration retrieved successfully", "CONFIG_FETCH_FAILED": "Failed to retrieve configuration", "DASHBOARD_DATA_FETCHED": "Dashboard data retrieved successfully", "DASHBOARD_DATA_FETCH_FAILED": "Failed to retrieve dashboard data", "SYSTEM_STATUS_FETCHED": "System status retrieved successfully", "SYSTEM_STATUS_FETCH_FAILED": "Failed to retrieve system status", "KNOWLEDGE_BASE_UPDATED": "Knowledge base updated successfully", "KNOWLEDGE_BASE_UPDATE_FAILED": "Failed to update knowledge base", "SUGGESTIONS_FETCHED": "Suggestions retrieved successfully", "SUGGESTIONS_FETCH_FAILED": "Failed to retrieve suggestions", "SLA_WARNING": "SLA deadline approaching", "SLA_BREACH": "SLA deadline exceeded", "TICKET_RESOLVED": "Ticket has been resolved", "TICKET_CLOSED": "Ticket has been closed", "CUSTOMER_SATISFACTION_RECORDED": "Customer satisfaction rating recorded", "EXPERT_ASSIGNED": "Expert has been assigned to your ticket", "AUTO_ESCALATION": "Ticket has been automatically escalated", "MANUAL_ESCALATION": "Ticket has been manually escalated", "PRIORITY_ESCALATED": "Ticket priority has been escalated", "ORGANIZATION_ACCESS_DENIED": "Access denied for this organization", "SUPER_ADMIN_REQUIRED": "Super administrator privileges required", "ADMIN_REQUIRED": "Administrator privileges required", "AGENT_REQUIRED": "Agent privileges required", "TICKET_OWNER_ONLY": "Only ticket owner can perform this action", "INTERNAL_SERVER_ERROR": "Internal server error occurred", "SERVICE_UNAVAILABLE": "Service temporarily unavailable", "MAINTENANCE_MODE": "System is under maintenance", "FEATURE_DISABLED": "This feature is currently disabled", "INVALID_REQUEST": "Invalid request format", "MISSING_PARAMETERS": "Required parameters are missing", "INVALID_PARAMETERS": "Invalid parameter values provided", "OPERATION_SUCCESSFUL": "Operation completed successfully", "OPERATION_FAILED": "Operation failed", "DATA_NOT_FOUND": "Requested data not found", "DUPLICATE_ENTRY": "Duplicate entry detected", "CONFLICT_ERROR": "Conflict with existing data", "PRECONDITION_FAILED": "Precondition failed", "RESOURCE_LOCKED": "Resource is currently locked", "QUOTA_EXCEEDED": "Quota limit exceeded", "SESSION_EXPIRED": "Session has expired", "ACCOUNT_SUSPENDED": "Account has been suspended", "ACCOUNT_LOCKED": "Account is temporarily locked", "PASSWORD_EXPIRED": "Password has expired", "TWO_FACTOR_REQUIRED": "Two-factor authentication required", "EMAIL_VERIFICATION_REQUIRED": "Email verification required", "PHONE_VERIFICATION_REQUIRED": "Phone verification required", "TERMS_ACCEPTANCE_REQUIRED": "Terms and conditions acceptance required", "PRIVACY_POLICY_ACCEPTANCE_REQUIRED": "Privacy policy acceptance required", "GDPR_CONSENT_REQUIRED": "GDPR consent required", "DATA_RETENTION_POLICY": "Data retention policy applies", "BACKUP_COMPLETED": "Backup completed successfully", "BACKUP_FAILED": "Backup operation failed", "RESTORE_COMPLETED": "Restore completed successfully", "RESTORE_FAILED": "Restore operation failed", "SYNC_COMPLETED": "Synchronization completed successfully", "SYNC_FAILED": "Synchronization failed", "MIGRATION_COMPLETED": "Migration completed successfully", "MIGRATION_FAILED": "Migration failed", "HEALTH_CHECK_PASSED": "Health check passed", "HEALTH_CHECK_FAILED": "Health check failed", "PERFORMANCE_DEGRADED": "Performance degradation detected", "PERFORMANCE_OPTIMAL": "System performance is optimal", "SECURITY_ALERT": "Security alert triggered", "SECURITY_BREACH": "Security breach detected", "AUDIT_LOG_CREATED": "Audit log entry created", "COMPLIANCE_CHECK_PASSED": "Compliance check passed", "COMPLIANCE_CHECK_FAILED": "Compliance check failed", "SOMETHING_WENT_WRONG": "Something went wrong", "SUCCESS_DATA_FETCHED": "Data fetched successfully", "SUCCESS_DATA_CREATED": "Data created successfully", "SUCCESS_DATA_UPDATED": "Data updated successfully", "SUCCESS_DATA_RETRIEVED": "Data retrieved successfully", "ERROR_CREATING_TICKET": "Error creating ticket", "ERROR_UPDATING_TICKET": "Error updating ticket", "ERROR_DELETING_TICKET": "Error deleting ticket", "ERROR_ASSIGNING_TICKET": "Error assigning ticket", "ERROR_UPDATING_TICKET_STATUS": "Error updating ticket status", "ERROR_FETCHING_TICKETS": "Error fetching tickets", "ERROR_FETCHING_TICKET": "Error fetching ticket", "TICKET_STATUS_UPDATED": "Ticket status updated successfully", "PERMISSION_DENIED": "Permission denied", "ACCESS_DENIED": "Access denied", "FORBIDDEN": "Forbidden", "BAD_REQUEST": "Bad request", "NOT_FOUND": "Not found", "METHOD_NOT_ALLOWED": "Method not allowed", "CONFLICT": "Conflict occurred", "UNPROCESSABLE_ENTITY": "Unprocessable entity", "TOO_MANY_REQUESTS": "Too many requests", "DATABASE_ERROR": "Database error occurred", "DATABASE_CONNECTION_ERROR": "Database connection error", "FILE_UPLOAD_ERROR": "File upload error", "FILE_SIZE_TOO_LARGE": "File size is too large", "FILE_TYPE_NOT_ALLOWED": "File type is not allowed", "INVALID_FILE_FORMAT": "Invalid file format", "NO_FILE_UPLOADED": "No file uploaded", "FILES_UPLOADED_SUCCESSFULLY": "Files uploaded successfully", "OPERATION_NOT_ALLOWED": "Operation not allowed", "RESOURCE_NOT_FOUND": "Resource not found", "INVALID_DATA_FORMAT": "Invalid data format", "MISSING_REQUIRED_FIELDS": "Missing required fields", "ORGANIZATION_ID_REQUIRED": "Organization ID is required", "TICKET_ID_REQUIRED": "Ticket ID is required", "MESSAGE_ID_REQUIRED": "Message ID is required", "USER_ID_REQUIRED": "User ID is required", "INVALID_TICKET_ID": "Invalid ticket ID", "INVALID_MESSAGE_ID": "Invalid message ID", "INVALID_USER_ID": "Invalid user ID", "INVALID_ORGANIZATION_ID": "Invalid organization ID", "CONFIG_CREATED_SUCCESSFULLY": "Configuration created successfully", "CONFIG_UPDATED_SUCCESSFULLY": "Configuration updated successfully", "CONFIG_FETCHED_SUCCESSFULLY": "Configuration retrieved successfully", "CONFIG_NOT_FOUND": "Configuration not found", "ERROR_UPDATING_CONFIG": "Error updating configuration", "ERROR_FETCHING_CONFIG": "Error fetching configuration", "PIN_VALIDATED_SUCCESSFULLY": "PIN validated successfully", "PIN_VALIDATION_FAILED": "PIN validation failed", "ERROR_VALIDATING_PIN": "Error validating PIN", "INVALID_PIN": "Invalid PIN", "MESSAGE_CREATED_SUCCESSFULLY": "Message created successfully", "ERROR_SENDING_MESSAGE": "Error sending message", "ERROR_FETCHING_MESSAGES": "Error fetching messages", "TICKET_PRIORITY_UPDATED": "Ticket priority updated successfully", "TICKET_CLOSED_SUCCESSFULLY": "Ticket closed successfully", "TICKET_REOPENED_SUCCESSFULLY": "Ticket reopened successfully", "TICKET_RESOLVED_SUCCESSFULLY": "Ticket resolved successfully", "ANALYTICS_FETCHED_SUCCESSFULLY": "Analytics data fetched successfully", "STATISTICS_FETCHED_SUCCESSFULLY": "Statistics fetched successfully", "BULK_OPERATION_COMPLETED": "Bulk operation completed successfully", "EXPORT_COMPLETED_SUCCESSFULLY": "Export completed successfully", "IMPORT_COMPLETED_SUCCESSFULLY": "Import completed successfully", "SUPPORT_PIN_REQUIRED": "Support PIN is required to create a ticket", "INVALID_SUPPORT_PIN": "Invalid support PIN. Please check your PIN and try again", "SUPPORT_CONFIG_NOT_FOUND": "Support configuration not found for this organization", "SUPPORT_PIN_VALID": "Support PIN is valid", "COMMENT_ADDED_SUCCESSFULLY": "Comment added to ticket successfully", "COMMENT_ADD_FAILED": "Failed to add comment to ticket", "COMMENTS_FETCHED_SUCCESSFULLY": "Ticket comments retrieved successfully", "COMMENTS_FETCH_FAILED": "Failed to retrieve ticket comments", "INTERNAL_COMMENT_ADDED": "Internal comment added successfully", "PUBLIC_COMMENT_ADDED": "Public comment added successfully", "TICKET_STATUS_UPDATED_SUCCESSFULLY": "Support ticket status updated successfully", "TICKET_STATUS_UPDATE_FAILED": "Failed to update support ticket status", "TICKET_MODULE_UPDATED": "Support ticket module type updated successfully", "TICKET_MODULE_UPDATE_FAILED": "Failed to update support ticket module type", "INVALID_TICKET_IDENTIFIER": "Invalid ticket identifier provided", "INVALID_TICKET_STATUS": "Invalid ticket status provided", "INVALID_PRIORITY": "Invalid priority level provided", "INVALID_MODULE_TYPE": "Invalid module type provided", "INVALID_ISSUE_TYPE": "Invalid issue type provided", "INVALID_MESSAGE_TYPE": "Invalid message type provided", "FILE_UPLOAD_SUCCESS": "File uploaded successfully", "FILE_SIZE_EXCEEDED": "File size exceeds maximum allowed limit", "MAX_FILES_EXCEEDED": "Maximum number of files exceeded", "VIDEO_FILE_TOO_LARGE": "Video file size exceeds 200 MB limit", "IMAGE_FILE_TOO_LARGE": "Image size exceeds 10 MB limit", "REQUIRED_FIELD_MISSING": "Required field is missing", "INVALID_EMAIL_FORMAT": "Invalid email format provided", "INVALID_PHONE_FORMAT": "Invalid phone number format", "TEXT_TOO_LONG": "Text exceeds maximum allowed length", "TEXT_TOO_SHORT": "Text is too short. Minimum length required", "ORGANIZATION_NOT_FOUND": "Organization not found", "ORGANIZATION_DISABLED": "Organization is disabled", "ORGANIZATION_INACTIVE": "Organization is inactive", "USER_INACTIVE": "User account is inactive", "USER_UNAUTHORIZED": "User is not authorized for this action", "AGENT_ASSIGNED": "Agent assigned to ticket successfully", "AGENT_UNASSIGNED": "Agent unassigned from ticket successfully", "AGENT_NOT_FOUND": "Agent not found", "PRIORITY_UPDATED": "Ticket priority updated successfully", "STATUS_CHANGED": "Ticket status changed successfully", "MODULE_ASSIGNED": "Module type assigned successfully", "ATTACHMENT_ADDED": "Attachment added successfully", "ATTACHMENT_REMOVED": "Attachment removed successfully", "ATTACHMENT_NOT_FOUND": "Attachment not found", "HISTORY_RECORDED": "Action recorded in ticket history", "HISTORY_FETCH_FAILED": "Failed to retrieve ticket history", "HISTORY_FETCHED_SUCCESSFULLY": "Ticket history retrieved successfully", "SEARCH_RESULTS_FOUND": "Search results found", "NO_SEARCH_RESULTS": "No search results found", "SEARCH_QUERY_INVALID": "Invalid search query", "PAGINATION_ERROR": "Pagination parameters are invalid", "PAGE_NOT_FOUND": "Requested page not found", "FILTER_APPLIED": "Filters applied successfully", "FILTER_INVALID": "Invalid filter parameters", "EMAIL_SENT": "<PERSON>ail sent successfully", "EMAIL_FAILED": "Failed to send email", "CACHE_CLEARED": "<PERSON><PERSON> cleared successfully", "CACHE_ERROR": "Cache operation failed", "REQUEST_TIMEOUT": "Request timeout. Please try again", "SUCCESS": "Operation completed successfully", "ERROR": "An error occurred", "WARNING": "Warning: Please review the information", "INFO": "Information updated", "CREATED": "Created successfully", "UPDATED": "Updated successfully", "DELETED": "Deleted successfully", "RETRIEVED": "Retrieved successfully", "USERS_FETCHED_SUCCESSFULLY": "Users retrieved successfully", "ERROR_FETCHING_USERS": "Error fetching users", "USER_DETAILS_FETCHED_SUCCESSFULLY": "User details retrieved successfully", "ERROR_FETCHING_USER_DETAILS": "Error fetching user details", "USER_WORKLOADS_FETCHED_SUCCESSFULLY": "User workloads retrieved successfully", "ERROR_FETCHING_USER_WORKLOADS": "Error fetching user workloads", "ASSIGNEE_SUGGESTION_GENERATED": "Assignee suggestion generated successfully", "ERROR_SUGGESTING_ASSIGNEE": "Error generating assignee suggestion", "NO_ASSIGNABLE_USERS_FOUND": "No assignable users found", "TICKET_DATA_REQUIRED": "Ticket data is required", "ASSIGNED_USER_ID_REQUIRED": "Assigned user ID is required", "ASSIGNEE_NOT_FOUND": "Assignee not found", "ASSIGNEE_ORGANIZATION_MISMATCH": "Assignee must be from the same organization", "ASSIGNEE_NOT_ACTIVE": "Assignee account is not active", "ASSIGNEE_INSUFFICIENT_PERMISSIONS": "Assignee has insufficient permissions", "ASSIGNEE_OUTSIDE_ORG": "Cannot assign ticket to a user outside your organization", "INVALID_ASSIGNEE": "Assignee user does not exist or is inactive", "ATTACHMENTS_DISABLED": "File attachments are disabled for this organization", "MESSAGE_REQUIRED": "Message is required", "CONTENT_REQUIRED": "Content is required", "TICKET_CONTENT_REQUIRED": "Ticket content is required", "TICKET_ANALYZED_SUCCESSFULLY": "Ticket analyzed successfully", "TICKET_ANALYSIS_FAILED": "Ticket analysis failed", "RESPONSE_GENERATED_SUCCESSFULLY": "Response generated successfully", "NO_RESPONSE_GENERATED": "No response generated", "RESPONSE_GENERATION_FAILED": "Response generation failed", "ISSUE_TYPE_REQUIRED": "Issue type is required", "TROUBLESHOOTING_STEPS_FETCHED": "Troubleshooting steps fetched successfully", "TROUBLESHOOTING_STEPS_FETCH_FAILED": "Troubleshooting steps fetch failed", "DASHBOARD_FETCHED_SUCCESSFULLY": "Dashboard data fetched successfully", "DASHBOARD_FETCH_FAILED": "Failed to fetch dashboard data", "OPERATION_COMPLETED": "Operation completed successfully", "INVALID_OPERATION": "Invalid operation", "TICKET_DELETED": "Ticket deleted successfully", "BULK_OPERATION_FAILED": "Bulk operation failed", "TICKET_IDS_REQUIRED": "Ticket IDs are required", "_comment_auth_messages": "Authentication and Authorization Messages", "ERROR_TOKEN_REQUIRED": "Authentication token is required", "ERROR_INVALID_TOKEN": "Invalid or expired authentication token", "ERROR_ORGANIZATION_REQUIRED": "Organization ID is required in token", "_comment_ticket_messages": "Ticket Operation Messages", "NO_TICKETS_FOUND": "No tickets found", "TICKETS_RETRIEVED_SUCCESSFULLY": "Tickets retrieved successfully", "TICKET_RETRIEVED_SUCCESSFULLY": "Ticket retrieved successfully", "TICKET_DELETED_SUCCESSFULLY": "Ticket deleted successfully", "TICKET_RATED_SUCCESSFULLY": "Ticket rated successfully", "_comment_message_messages": "Message Operation Messages", "_comment_config_messages": "Configuration Messages", "DEFAULT_CONFIG_NOT_FOUND": "Default configuration not found", "DEFAULT_CONFIG_FETCHED_SUCCESSFULLY": "Default configuration fetched successfully", "DEFAULT_CONFIG_CREATED_SUCCESSFULLY": "Default configuration created successfully", "DEFAULT_CONFIG_UPDATED_SUCCESSFULLY": "Default configuration updated successfully", "_comment_admin_messages": "Admin Operation Messages", "_comment_validation_messages": "Validation Error Messages", "VALIDATION_FAILED": "Validation failed", "REQUIRED_FIELD": "This field is required", "INVALID_FORMAT": "Invalid format provided", "FIELD_TOO_SHORT": "Field value is too short", "FIELD_TOO_LONG": "Field value is too long", "INVALID_VALUE": "Invalid value provided", "_comment_file_messages": "File Upload Messages", "FILE_VALIDATION_FAILED": "File validation failed", "INVALID_FILE_EXTENSION": "Invalid file extension", "FILE_UPLOAD_LIMIT_EXCEEDED": "File upload limit exceeded", "ATTACHMENT_UPLOAD_FAILED": "Attachment upload failed", "ATTACHMENT_DELETED_SUCCESSFULLY": "Attachment deleted successfully", "_comment_system_messages": "System Status Messages", "SYSTEM_HEALTHY": "System is healthy", "SYSTEM_MAINTENANCE": "System is under maintenance", "SERVICE_UNAVAILABLE_TEMPORARILY": "Service temporarily unavailable", "RATE_LIMIT_EXCEEDED_DETAILS": "Rate limit exceeded. Please wait before trying again", "_comment_notification_messages": "Notification Messages", "NOTIFICATION_SENT_SUCCESSFULLY": "Notification sent successfully", "EMAIL_NOTIFICATION_FAILED": "Email notification failed", "SMS_NOTIFICATION_FAILED": "SMS notification failed", "_comment_search_filter_messages": "Search and Filter Messages", "SEARCH_TERM_TOO_SHORT": "Search term is too short", "INVALID_SEARCH_CRITERIA": "Invalid search criteria", "FILTER_RESULTS_EMPTY": "No results match the filter criteria", "SORT_PARAMETER_INVALID": "Invalid sort parameter", "_comment_permission_messages": "Permission Messages", "ROLE_INSUFFICIENT": "Insufficient role permissions", "FEATURE_ACCESS_DENIED": "Access denied to this feature", "ORGANIZATION_ACCESS_REQUIRED": "Organization access required", "ADMIN_ACCESS_REQUIRED": "Administrator access required", "_comment_status_messages": "Status Change Messages", "STATUS_CHANGE_SUCCESSFUL": "Status changed successfully", "STATUS_CHANGE_FAILED": "Status change failed", "INVALID_STATUS_TRANSITION": "Invalid status transition", "STATUS_ALREADY_SET": "Status is already set to this value", "_comment_assignment_messages": "Assignment Messages", "ASSIGNMENT_SUCCESSFUL": "Assignment successful", "ASSIGNMENT_FAILED": "Assignment failed", "UNASSIGNMENT_SUCCESSFUL": "Unassignment successful", "AGENT_ALREADY_ASSIGNED": "Agent is already assigned to this ticket", "AGENT_NOT_AVAILABLE": "Agent is not available for assignment", "_comment_api_messages": "API Response Messages", "REQUEST_PROCESSED": "Request processed successfully", "REQUEST_FAILED": "Request processing failed", "INVALID_REQUEST_METHOD": "Invalid request method", "ENDPOINT_NOT_FOUND": "API endpoint not found", "API_VERSION_UNSUPPORTED": "API version not supported", "_comment_export_import_messages": "Export/Import Messages", "EXPORT_STARTED": "Export process started", "EXPORT_FAILED": "Export process failed", "IMPORT_STARTED": "Import process started", "IMPORT_FAILED": "Import process failed", "DATA_FORMAT_INVALID": "Invalid data format for import", "_comment_performance_messages": "Performance Messages", "QUERY_TIMEOUT": "Database query timeout", "PROCESSING_SLOW": "Processing is slower than expected", "OPTIMIZATION_RECOMMENDED": "Performance optimization recommended", "_comment_security_messages": "Security Messages", "SECURITY_TOKEN_EXPIRED": "Security token has expired", "IP_ADDRESS_BLOCKED": "IP address is blocked", "SUSPICIOUS_ACTIVITY": "Suspicious activity detected", "PASSWORD_POLICY_VIOLATION": "Password does not meet policy requirements", "_comment_integration_messages": "Integration Messages", "EXTERNAL_SERVICE_ERROR": "External service error", "API_QUOTA_EXCEEDED": "API quota exceeded", "WEBHOOK_DELIVERY_FAILED": "Webhook delivery failed", "SYNC_OPERATION_FAILED": "Synchronization operation failed", "TICKET_COMMENT_ADDED": "Comment added to ticket successfully", "TICKET_COMMENTS_RETRIEVED": "Ticket comments retrieved successfully", "USERS_RETRIEVED_SUCCESSFULLY": "Users retrieved successfully", "_comment_advanced_ticket_features": "Advanced Ticket Features", "FOLLOWERS_ADDED_SUCCESSFULLY": "Team members have been added to track your ticket progress", "FOLLOWERS_UPDATED_SUCCESSFULLY": "Ticket followers updated successfully", "INVALID_FOLLOWER": "One or more follower user IDs are invalid", "FOLLOWER_NOT_FOUND": "Follower user not found", "MANUAL_DATES_UPDATED": "Timeline has been updated for your ticket", "MANUAL_START_DATE_SET": "Custom start date has been set for your ticket", "MANUAL_DUE_DATE_SET": "Custom due date has been set for your ticket", "DATE_OVERRIDE_SUCCESSFUL": "Custom timeline has been applied to your ticket", "INVALID_DATE_RANGE": "Start date must be before due date", "MANUAL_DATES_PERMISSION_DENIED": "Only agents can set manual dates", "SLA_PAUSED_SUCCESSFULLY": "Ticket timeline has been paused due to holidays/maintenance", "SLA_RESUMED_SUCCESSFULLY": "Ticket timeline has been resumed", "SLA_PAUSE_PERMISSION_DENIED": "Only agents can pause SLA", "SLA_PAUSE_REASON_REQUIRED": "Reason is required when pausing SLA", "TICKET_PRIORITY_CHANGED": "Your ticket priority has been updated", "TICKET_STATUS_CHANGED": "Your ticket status has been updated", "TICKET_ASSIGNED_TO_TEAM": "Your ticket has been assigned to our support team", "PIN_VERIFICATION_SUCCESSFUL": "Support PIN verified successfully", "PIN_VERIFICATION_FAILED": "Invalid support PIN provided", "PIN_REQUIRED_FOR_UPDATE": "Support PIN is required for this update", "ORGANIZATION_NAME_FETCHED": "Organization information retrieved successfully", "ORGANIZATION_NAME_FETCH_FAILED": "Failed to retrieve organization information", "ATTACHMENT_RESPONSE_SIMPLIFIED": "File information retrieved successfully", "ATTACHMENT_FIELDS_OPTIMIZED": "File details optimized for display", "USER_FRIENDLY_UPDATE_SUCCESS": "Your support ticket has been updated successfully", "TEAM_COLLABORATION_ENABLED": "Team collaboration features enabled for this ticket", "FLEXIBLE_SLA_ENABLED": "Flexible SLA management enabled for this ticket", "FESTIVAL_SLA_PAUSE": "Ticket timeline paused for festival holidays", "MAINTENANCE_SLA_PAUSE": "Ticket timeline paused for system maintenance", "CUSTOM_TIMELINE_APPLIED": "Custom timeline has been applied by our support team", "PRIORITY_SLA_CONFIG_UPDATED": "Priority-specific SLA configuration updated successfully", "SLA_LOW_PRIORITY_SET": "SLA time for low priority tickets updated", "SLA_MEDIUM_PRIORITY_SET": "SLA time for medium priority tickets updated", "SLA_HIGH_PRIORITY_SET": "SLA time for high priority tickets updated", "SLA_URGENT_PRIORITY_SET": "SLA time for urgent priority tickets updated", "AGENT_OVERRIDE_APPLIED": "Agent override has been applied to your ticket", "TIMELINE_FLEXIBILITY_ENABLED": "Timeline flexibility enabled for better service", "REAL_WORLD_SLA_MANAGEMENT": "SLA management adapted for real-world scenarios", "_comment_config_security_validation": "Configuration Security & Validation Messages", "CONFIG_MODIFICATION_RESTRICTED": "Configuration modification is restricted to super administrators", "INVALID_SLA_URGENT": "Urgent SLA must be between 1-168 hours for system stability", "INVALID_SLA_HIERARCHY": "SLA times must follow hierarchy: urgent ≤ high ≤ medium ≤ low", "ATTACHMENT_SIZE_TOO_LARGE": "Maximum attachment size cannot exceed 100MB for system performance", "SYSTEM_WIDE_CONFIG_CHANGE": "System-wide configuration change detected - all organizations affected", "CONFIG_VALIDATION_FAILED": "Configuration validation failed - please check your settings", "SLA_HIERARCHY_VALIDATION_FAILED": "SLA hierarchy validation failed", "SLA_VALIDATION_FAILED": "SLA validation failed", "ATTACHMENT_SIZE_VALIDATION_FAILED": "Attachment size validation failed", "_comment_work_assignment_messages": "Work Assignment Messages", "ASSIGN_USER_TO_START_WORK": "Please assign a user to this ticket before starting work", "TICKET_MUST_BE_ASSIGNED": "Ticket must be assigned to a user before work can begin", "ASSIGNMENT_REQUIRED_FOR_WORK": "User assignment is required to start work on this ticket"}