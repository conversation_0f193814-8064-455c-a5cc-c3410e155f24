'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    await queryInterface.bulkInsert('mo_support_ticket_history', [
      // History for Ticket 1 (ORG-001-TKT-001)
      {
        ticket_id: 1,
        previous_status: null,
        new_status: 'OPEN',
        change_note: 'Ticket created via public submission',
        created_by: 0,
        created_at: twoDaysAgo
      },

      // History for Ticket 2 (ORG-001-TKT-002)
      {
        ticket_id: 2,
        previous_status: null,
        new_status: 'OPEN',
        change_note: 'Ticket created via public submission',
        created_by: 0,
        created_at: twoDaysAgo
      },
      {
        ticket_id: 2,
        previous_status: 'OPEN',
        new_status: 'ASSIGNED',
        change_note: 'Ticket assigned to development team for feature evaluation',
        created_by: 1,
        created_at: yesterday
      },

      // History for Ticket 3 (ORG-002-TKT-001) - Critical bug
      {
        ticket_id: 3,
        previous_status: null,
        new_status: 'OPEN',
        change_note: 'Critical bug reported - system crashes on project creation',
        created_by: 0,
        created_at: yesterday
      },
      {
        ticket_id: 3,
        previous_status: 'OPEN',
        new_status: 'ASSIGNED',
        change_note: 'Assigned to senior developer due to critical priority',
        created_by: 1,
        created_at: yesterday
      },
      {
        ticket_id: 3,
        previous_status: 'ASSIGNED',
        new_status: 'IN_PROGRESS',
        change_note: 'Developer started working on the issue',
        created_by: 3,
        created_at: yesterday
      },

      // History for Ticket 4 (ORG-002-TKT-002) - Resolved ticket
      {
        ticket_id: 4,
        previous_status: null,
        new_status: 'OPEN',
        change_note: 'User inquiry about automated reports setup',
        created_by: 0,
        created_at: twoDaysAgo
      },
      {
        ticket_id: 4,
        previous_status: 'OPEN',
        new_status: 'ASSIGNED',
        change_note: 'Assigned to support agent for assistance',
        created_by: 1,
        created_at: twoDaysAgo
      },
      {
        ticket_id: 4,
        previous_status: 'ASSIGNED',
        new_status: 'IN_PROGRESS',
        change_note: 'Agent started providing step-by-step guidance',
        created_by: 2,
        created_at: twoDaysAgo
      },
      {
        ticket_id: 4,
        previous_status: 'IN_PROGRESS',
        new_status: 'RESOLVED',
        change_note: 'User successfully set up automated reports with provided guidance',
        created_by: 2,
        created_at: yesterday
      },

      // History for Ticket 5 (ORG-003-TKT-001)
      {
        ticket_id: 5,
        previous_status: null,
        new_status: 'OPEN',
        change_note: 'API integration inquiry submitted',
        created_by: 0,
        created_at: yesterday
      },
      {
        ticket_id: 5,
        previous_status: 'OPEN',
        new_status: 'ASSIGNED',
        change_note: 'Assigned to technical support specialist',
        created_by: 1,
        created_at: yesterday
      },
      {
        ticket_id: 5,
        previous_status: 'ASSIGNED',
        new_status: 'UNDER_REVIEW',
        change_note: 'Reviewing integration requirements and available documentation',
        created_by: 3,
        created_at: yesterday
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('mo_support_ticket_history', null, {});
  }
};
