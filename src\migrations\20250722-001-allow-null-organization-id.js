"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Allow organization_id to be null in mo_support_tickets table
    await queryInterface.changeColumn("mo_support_tickets", "organization_id", {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    // Revert back to not allowing null
    // Note: This will fail if there are any null values in the database
    await queryInterface.changeColumn("mo_support_tickets", "organization_id", {
      type: Sequelize.STRING,
      allowNull: false,
    });
  },
};
