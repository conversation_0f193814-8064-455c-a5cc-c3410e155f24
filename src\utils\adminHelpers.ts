import { Op } from "sequelize";
import Ticket from "../models/Ticket";
import { TICKET_STATUS, SORT_BY, SORT_ORDER } from "../helper/constant";
import TicketMessage from "../models/TicketMessage";
import TicketHistory from "../models/TicketHistory";

/**
 * Build dynamic where clause for ticket filtering
 */
export const buildTicketWhereClause = (
  filters: any,
  effectiveOrgId?: string | null
) => {
  const whereClause: any = {};

  // Organization filter
  if (effectiveOrgId !== undefined) {
    whereClause.organization_id = effectiveOrgId;
  }

  // Search functionality
  if (filters.search) {
    whereClause[Op.or] = [
      { ticket_number: { [Op.like]: `%${filters.search}%` } },
      { subject: { [Op.like]: `%${filters.search}%` } },
      { submitter_name: { [Op.like]: `%${filters.search}%` } },
      { submitter_email: { [Op.like]: `%${filters.search}%` } },
      { description: { [Op.like]: `%${filters.search}%` } },
    ];
  }

  // Status filter
  if (filters.status) {
    if (Array.isArray(filters.status)) {
      whereClause.ticket_status = { [Op.in]: filters.status };
    } else {
      whereClause.ticket_status = filters.status;
    }
  }

  // Priority filter
  if (filters.priority) {
    if (Array.isArray(filters.priority)) {
      whereClause.priority = { [Op.in]: filters.priority };
    } else {
      whereClause.priority = filters.priority;
    }
  }

  // Module type filter
  if (filters.module_type) {
    whereClause.module_type = filters.module_type;
  }

  // Issue type filter
  if (filters.issue_type) {
    whereClause.issue_type = filters.issue_type;
  }

  // Assignment filter
  if (filters.assigned_to_user_id === "null" || filters.unassigned === "true") {
    whereClause.assigned_to_user_id = null;
  } else if (filters.assigned_to_user_id) {
    whereClause.assigned_to_user_id = filters.assigned_to_user_id;
  }

  // Submitter email filter
  if (filters.submitter_email) {
    whereClause.submitter_email = { [Op.like]: `%${filters.submitter_email}%` };
  }

  // Date range filter
  if (filters.date_from || filters.date_to) {
    whereClause.created_at = {};
    if (filters.date_from) {
      whereClause.created_at[Op.gte] = new Date(filters.date_from);
    }
    if (filters.date_to) {
      whereClause.created_at[Op.lte] = new Date(filters.date_to);
    }
  }

  // Overdue filter
  if (filters.overdue === "true") {
    whereClause.sla_due_date = { [Op.lt]: new Date() };
    whereClause.ticket_status = {
      [Op.notIn]: [TICKET_STATUS.RESOLVED, TICKET_STATUS.CLOSED],
    };
  }

  // Has rating filter
  if (filters.has_rating === "true") {
    whereClause.rating = { [Op.not]: null };
  } else if (filters.has_rating === "false") {
    whereClause.rating = null;
  }

  return whereClause;
};

/**
 * Get ticket statistics for dashboard
 */
export const getTicketStatistics = async (whereClause: any) => {
  const [
    totalTickets,
    ticketsByStatus,
    ticketsByPriority,
    ticketsByModule,
    overdueTickets,
    unassignedTickets,
  ] = await Promise.all([
    // Total tickets
    Ticket.count({ where: whereClause }),

    // Tickets by status
    Ticket.findAll({
      where: whereClause,
      attributes: [
        "ticket_status",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["ticket_status"],
      raw: true,
    }),

    // Tickets by priority
    Ticket.findAll({
      where: whereClause,
      attributes: [
        "priority",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["priority"],
      raw: true,
    }),

    // Tickets by module
    Ticket.findAll({
      where: whereClause,
      attributes: [
        "module_type",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["module_type"],
      raw: true,
    }),

    // Overdue tickets
    Ticket.count({
      where: {
        ...whereClause,
        sla_due_date: { [Op.lt]: new Date() },
        ticket_status: {
          [Op.notIn]: [TICKET_STATUS.RESOLVED, TICKET_STATUS.CLOSED],
        },
      },
    }),

    // Unassigned tickets
    Ticket.count({
      where: {
        ...whereClause,
        assigned_to_user_id: null,
        ticket_status: {
          [Op.notIn]: [TICKET_STATUS.RESOLVED, TICKET_STATUS.CLOSED],
        },
      },
    }),
  ]);

  return {
    totalTickets,
    ticketsByStatus,
    ticketsByPriority,
    ticketsByModule,
    overdueTickets,
    unassignedTickets,
  };
};

/**
 * Get SLA and performance metrics
 */
export const getSLAMetrics = async (whereClause: any) => {
  const [avgResponseTime, avgResolutionTime, slaCompliance] = await Promise.all(
    [
      // Average response time
      Ticket.findOne({
        where: {
          ...whereClause,
          first_response_at: { [Op.not]: null },
        },
        attributes: [
          [
            Ticket.sequelize!.fn(
              "AVG",
              Ticket.sequelize!.literal(
                "TIMESTAMPDIFF(HOUR, created_at, first_response_at)"
              )
            ),
            "avg_hours",
          ],
        ],
        raw: true,
      }),

      // Average resolution time
      Ticket.findOne({
        where: {
          ...whereClause,
          resolved_at: { [Op.not]: null },
        },
        attributes: [
          [
            Ticket.sequelize!.fn(
              "AVG",
              Ticket.sequelize!.literal(
                "TIMESTAMPDIFF(HOUR, created_at, resolved_at)"
              )
            ),
            "avg_hours",
          ],
        ],
        raw: true,
      }),

      // SLA compliance rate (only for tickets with SLA enabled)
      Ticket.findAll({
        where: {
          ...whereClause,
          sla_due_date: { [Op.not]: null },
        },
        include: [
          {
            model: db.SupportConfig,
            as: "supportConfig",
            where: { sla_enabled: true },
            required: true, // INNER JOIN - only tickets with SLA enabled
          },
        ],
        attributes: [
          [
            Ticket.sequelize!.fn(
              "SUM",
              Ticket.sequelize!.literal(
                "CASE WHEN resolved_at <= sla_due_date THEN 1 ELSE 0 END"
              )
            ),
            "on_time",
          ],
          [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "total"],
        ],
        raw: true,
      }),
    ]
  );

  return {
    avgResponseTimeHours: (avgResponseTime as any)?.avg_hours || 0,
    avgResolutionTimeHours: (avgResolutionTime as any)?.avg_hours || 0,
    slaComplianceRate: slaCompliance[0]
      ? ((slaCompliance[0] as any).on_time / (slaCompliance[0] as any).total) *
        100
      : 0,
  };
};

/**
 * Get recent activity for dashboard
 */
export const getRecentActivity = async (
  whereClause: any,
  limit: number = 10
) => {
  const [recentTickets, recentMessages] = await Promise.all([
    // Recent tickets
    Ticket.findAll({
      where: whereClause,
      order: [[SORT_BY.CREATED_AT, SORT_ORDER.DESC]],
      limit,
      attributes: [
        "id",
        "ticket_number",
        "subject",
        "ticket_status",
        "priority",
        "created_at",
        "submitter_name",
      ],
    }),

    // Recent messages
    TicketMessage.findAll({
      include: [
        {
          model: Ticket,
          as: "ticket",
          where: whereClause,
          attributes: ["ticket_number", "subject"],
        },
      ],
      order: [[SORT_BY.CREATED_AT, SORT_ORDER.DESC]],
      limit,
      attributes: ["id", "message_type", "created_at", "ticket_id"],
    }),
  ]);

  return {
    recentTickets,
    recentMessages,
  };
};

/**
 * Validate sort parameters
 */
export const validateSortParams = (sortBy: string, sortOrder: string) => {
  const validSortFields = [
    SORT_BY.CREATED_AT,
    SORT_BY.UPDATED_AT,
    "ticket_number",
    "subject",
    "priority",
    SORT_BY.STATUS,
    "submitter_name",
    "assigned_at",
    "resolved_at",
    "sla_due_date",
  ];

  const validSortOrders = Object.values(SORT_ORDER);

  return {
    sortBy: validSortFields.includes(sortBy) ? sortBy : SORT_BY.CREATED_AT,
    sortOrder: validSortOrders.includes(sortOrder.toUpperCase() as any)
      ? (sortOrder.toUpperCase() as any)
      : SORT_ORDER.DESC,
  };
};

/**
 * Format pagination response (DEPRECATED - Use common.formatPaginatedResponse instead)
 * @deprecated Use formatPaginatedResponse from utils/common.ts for consistent pagination
 */
export const formatPaginationResponse = (
  count: number,
  page: number,
  limit: number
) => {
  console.warn(
    "DEPRECATED: formatPaginationResponse from adminHelpers.ts is deprecated. Use formatPaginatedResponse from utils/common.ts instead."
  );
  return {
    current_page: Number(page),
    total_pages: Math.ceil(count / Number(limit)),
    total_records: count,
    per_page: Number(limit),
    has_next_page: page < Math.ceil(count / Number(limit)),
    has_prev_page: page > 1,
  };
};

/**
 * Generate quick action items for dashboard
 */
export const generateQuickActions = (stats: any) => {
  const actions = [
    {
      title: "View All Tickets",
      description: "Manage all support tickets",
      icon: "ticket",
      action: "/admin/tickets",
      color: "primary",
      count: stats.totalTickets,
    },
  ];

  if (stats.unassignedTickets > 0) {
    actions.push({
      title: "Unassigned Tickets",
      description: `${stats.unassignedTickets} tickets need assignment`,
      icon: "assignment",
      action: "/admin/tickets?unassigned=true",
      color: "warning",
      count: stats.unassignedTickets,
    });
  }

  if (stats.overdueTickets > 0) {
    actions.push({
      title: "Overdue Tickets",
      description: `${stats.overdueTickets} tickets are overdue`,
      icon: "clock",
      action: "/admin/tickets?overdue=true",
      color: "danger",
      count: stats.overdueTickets,
    });
  }

  actions.push({
    title: "Support Configuration",
    description: "Manage organization settings",
    icon: "settings",
    action: "/admin/config",
    color: "info",
    count: null,
  });

  return actions;
};
