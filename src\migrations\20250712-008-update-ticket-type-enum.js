"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Step 1: Add a temporary column with the new enum values
    await queryInterface.addColumn("mo_support_tickets", "ticket_type_new", {
      type: Sequelize.ENUM(
        "bug_report",
        "feature_request",
        "general_inquiry",
        "export_help",
        "technical_issue",
        "non_technical_issue",
        "account_issue",
        "billing",
        "performance",
        "notifications",
        "support"
      ),
      allowNull: true,
      comment: "New ticket type enum values",
    });

    // Step 2: Migrate existing data to new values
    await queryInterface.sequelize.query(`
      UPDATE mo_support_tickets
      SET ticket_type_new = CASE
        WHEN ticket_type = 'PROBLEM' THEN 'bug_report'
        WHEN ticket_type = 'CHANGE_REQUEST' THEN 'feature_request'
        WHEN ticket_type = 'QUESTION' THEN 'general_inquiry'
        WHEN ticket_type = 'INCIDENT' THEN 'technical_issue'
        WHEN ticket_type = 'TASK' THEN 'support'
        ELSE 'general_inquiry'
      END;
    `);

    // Step 3: Drop the old column
    await queryInterface.removeColumn("mo_support_tickets", "ticket_type");

    // Step 4: Rename the new column to the original name
    await queryInterface.renameColumn(
      "mo_support_tickets",
      "ticket_type_new",
      "ticket_type"
    );

    // Step 5: Make the column NOT NULL with default value
    await queryInterface.changeColumn("mo_support_tickets", "ticket_type", {
      type: Sequelize.ENUM(
        "bug_report",
        "feature_request",
        "general_inquiry",
        "export_help",
        "technical_issue",
        "non_technical_issue",
        "account_issue",
        "billing",
        "performance",
        "notifications",
        "support"
      ),
      allowNull: false,
      defaultValue: "general_inquiry",
      comment: "Type of issue being reported",
    });
  },

  async down(queryInterface, Sequelize) {
    // Revert back to original enum values
    await queryInterface.changeColumn("mo_support_tickets", "ticket_type", {
      type: Sequelize.ENUM(
        "QUESTION",
        "INCIDENT",
        "PROBLEM",
        "TASK",
        "CHANGE_REQUEST"
      ),
      allowNull: false,
      defaultValue: "QUESTION",
      comment: "Type of issue being reported",
    });
  },
};
