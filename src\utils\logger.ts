/**
 * Logger utility for Customer Service Microservice
 * Simple CLI-friendly logging without external dependencies
 */

export enum LogLevel {
  ERROR = "error",
  WARN = "warn",
  INFO = "info",
  DEBUG = "debug",
}

export interface LogContext {
  userId?: string;
  organizationId?: string;
  ticketId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  [key: string]: any;
}

class Logger {
  private isDevelopment: boolean;
  private serviceName: string;
  private useJsonFormat: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === "development";
    this.serviceName = "customer-service-ms";
    // Use JSON format only in production for log aggregation
    this.useJsonFormat =
      process.env.LOG_FORMAT === "json" ||
      process.env.NODE_ENV === "production";
  }

  /**
   * Get timestamp
   */
  private getTimestamp(): string {
    const now = new Date();
    const timestamp = now.toISOString().replace("T", " ").replace("Z", "");
    return timestamp;
  }

  /**
   * Get log level
   */
  private getColoredLevel(level: LogLevel): string {
    switch (level) {
      case LogLevel.ERROR:
        return "ERROR";
      case LogLevel.WARN:
        return "WARN ";
      case LogLevel.INFO:
        return "INFO ";
      case LogLevel.DEBUG:
        return "DEBUG";
      default:
        return String(level).toUpperCase();
    }
  }

  /**
   * Get service name
   */
  private getServiceName(): string {
    return `[${this.serviceName}]`;
  }

  /**
   * Format context for CLI display
   */
  private formatContext(context?: LogContext): string {
    if (!context || Object.keys(context).length === 0) {
      return "";
    }

    const contextParts: string[] = [];

    // Handle special context fields
    if (context.port) {
      contextParts.push(`port=${context.port}`);
    }
    if (context.environment) {
      contextParts.push(`env=${context.environment}`);
    }
    if (context.apiDocs) {
      contextParts.push(`docs=${context.apiDocs}`);
    }
    if (context.healthCheck) {
      contextParts.push(`health=${context.healthCheck}`);
    }
    if (context.aiService) {
      contextParts.push(`ai=${context.aiService}`);
    }

    // Handle other context fields
    Object.entries(context).forEach(([key, value]) => {
      if (
        ![
          "port",
          "environment",
          "apiDocs",
          "healthCheck",
          "aiService",
        ].includes(key)
      ) {
        if (typeof value === "object") {
          contextParts.push(`${key}=${JSON.stringify(value)}`);
        } else {
          contextParts.push(`${key}=${value}`);
        }
      }
    });

    return contextParts.length > 0 ? ` | ${contextParts.join(" ")}` : "";
  }

  /**
   * Format log message for CLI or JSON
   */
  private formatMessage(
    level: LogLevel,
    message: string,
    context?: LogContext
  ): string {
    if (this.useJsonFormat) {
      // JSON format for production/log aggregation
      const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        service: this.serviceName,
        message,
        ...context,
      };
      return JSON.stringify(logEntry);
    } else {
      // Beautiful CLI format for development/staging
      const timestamp = this.getTimestamp();
      const coloredLevel = this.getColoredLevel(level);
      const serviceName = this.getServiceName();
      const contextStr = this.formatContext(context);

      return `${timestamp} ${coloredLevel} ${serviceName} ${message}${contextStr}`;
    }
  }

  /**
   * Log error messages
   */
  error(message: string, error?: Error | any, context?: LogContext): void {
    const errorContext = {
      ...context,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: this.isDevelopment ? error.stack : undefined,
          }
        : undefined,
    };

    const formattedMessage = this.formatMessage(
      LogLevel.ERROR,
      message,
      errorContext
    );
    console.error(formattedMessage);
  }

  /**
   * Log warning messages
   */
  warn(message: string, context?: LogContext): void {
    const formattedMessage = this.formatMessage(
      LogLevel.WARN,
      message,
      context
    );
    console.warn(formattedMessage);
  }

  /**
   * Log info messages
   */
  info(message: string, context?: LogContext): void {
    const formattedMessage = this.formatMessage(
      LogLevel.INFO,
      message,
      context
    );
    console.log(formattedMessage);
  }

  /**
   * Log debug messages (only in development)
   */
  debug(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      const formattedMessage = this.formatMessage(
        LogLevel.DEBUG,
        message,
        context
      );
      console.debug(formattedMessage);
    }
  }

  /**
   * Log API requests
   */
  request(
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    context?: LogContext
  ): void {
    this.info("API Request", {
      method,
      url,
      statusCode,
      duration: `${duration}ms`,
      ...context,
    });
  }

  /**
   * Log database operations
   */
  database(
    operation: string,
    table: string,
    duration?: number,
    context?: LogContext
  ): void {
    this.debug("Database Operation", {
      operation,
      table,
      duration: duration ? `${duration}ms` : undefined,
      ...context,
    });
  }

  /**
   * Log authentication events
   */
  auth(
    event: string,
    userId?: string,
    success: boolean = true,
    context?: LogContext
  ): void {
    this.info("Authentication Event", {
      event,
      userId,
      success,
      ...context,
    });
  }

  /**
   * Log ticket operations
   */
  ticket(
    action: string,
    ticketId: string,
    userId?: string,
    context?: LogContext
  ): void {
    this.info("Ticket Operation", {
      action,
      ticketId,
      userId,
      ...context,
    });
  }

  /**
   * Log file operations
   */
  file(
    action: string,
    fileName: string,
    success: boolean,
    context?: LogContext
  ): void {
    this.info("File Operation", {
      action,
      fileName,
      success,
      ...context,
    });
  }

  /**
   * Log security events
   */
  security(
    event: string,
    severity: "low" | "medium" | "high" | "critical",
    context?: LogContext
  ): void {
    this.warn("Security Event", {
      event,
      severity,
      ...context,
    });
  }

  /**
   * Log performance metrics
   */
  performance(
    metric: string,
    value: number,
    unit: string,
    context?: LogContext
  ): void {
    this.info("Performance Metric", {
      metric,
      value,
      unit,
      ...context,
    });
  }

  /**
   * Special startup logging methods for beautiful CLI output
   */
  startup(message: string, context?: LogContext): void {
    if (!this.useJsonFormat) {
      const timestamp = this.getTimestamp();
      const serviceName = this.getServiceName();
      const contextStr = this.formatContext(context);

      console.log(
        `${timestamp} 🚀 STARTUP ${serviceName} ${message}${contextStr}`
      );
    } else {
      this.info(message, context);
    }
  }

  success(message: string, context?: LogContext): void {
    if (!this.useJsonFormat) {
      const timestamp = this.getTimestamp();
      const serviceName = this.getServiceName();
      const contextStr = this.formatContext(context);

      console.log(
        `${timestamp} ✅ SUCCESS ${serviceName} ${message}${contextStr}`
      );
    } else {
      this.info(message, context);
    }
  }

  banner(): void {
    if (!this.useJsonFormat) {
      console.log("");
      console.log(
        "╔══════════════════════════════════════════════════════════════╗"
      );
      console.log(
        "║              🎫 TTH Support Ticket Microservice              ║"
      );
      console.log(
        "║                    Production Ready v3.0                     ║"
      );
      console.log(
        "╚══════════════════════════════════════════════════════════════╝"
      );
      console.log("");
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Export default
export default logger;
