"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add priority-specific SLA configuration fields to support configs
    await queryInterface.addColumn("mo_support_configs", "sla_low_priority_hours", {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 120, // 5 days
      comment: "SLA resolution time for LOW priority tickets (in hours)",
    });

    await queryInterface.addColumn("mo_support_configs", "sla_medium_priority_hours", {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 72, // 3 days
      comment: "SLA resolution time for MEDIUM priority tickets (in hours)",
    });

    await queryInterface.addColumn("mo_support_configs", "sla_high_priority_hours", {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 48, // 2 days
      comment: "SLA resolution time for HIGH priority tickets (in hours)",
    });

    await queryInterface.addColumn("mo_support_configs", "sla_urgent_priority_hours", {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 24, // 1 day
      comment: "SLA resolution time for URGENT priority tickets (in hours)",
    });

    // Add indexes for better query performance
    await queryInterface.addIndex("mo_support_configs", {
      fields: ["sla_low_priority_hours"],
      name: "idx_support_configs_sla_low",
    });

    await queryInterface.addIndex("mo_support_configs", {
      fields: ["sla_medium_priority_hours"],
      name: "idx_support_configs_sla_medium",
    });

    await queryInterface.addIndex("mo_support_configs", {
      fields: ["sla_high_priority_hours"],
      name: "idx_support_configs_sla_high",
    });

    await queryInterface.addIndex("mo_support_configs", {
      fields: ["sla_urgent_priority_hours"],
      name: "idx_support_configs_sla_urgent",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_sla_low");
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_sla_medium");
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_sla_high");
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_sla_urgent");

    // Remove columns
    await queryInterface.removeColumn("mo_support_configs", "sla_low_priority_hours");
    await queryInterface.removeColumn("mo_support_configs", "sla_medium_priority_hours");
    await queryInterface.removeColumn("mo_support_configs", "sla_high_priority_hours");
    await queryInterface.removeColumn("mo_support_configs", "sla_urgent_priority_hours");
  },
};
