import { db } from "../models";
import { ACTION_TYPE } from "./constant";

interface TicketData {
  [key: string]: any;
}

/**
 * Compare old and new ticket data and insert history records.
 */
export const recordTicketHistory = async (
  oldTicket: TicketData,
  newTicket: TicketData,
  changedBy: number
) => {
  const changedFields: string[] = [];
  const importantFields = [
    "ticket_status",
    "ticket_priority",
    "assigned_to_user_id",
    "rating",
    "review_comment",
    "resolution_note",
    "ticket_title",
    "ticket_description",
  ];

  for (const field of importantFields) {
    if (oldTicket[field] !== newTicket[field]) {
      changedFields.push(field);

      let actionType: keyof typeof ACTION_TYPE = ACTION_TYPE.UPDATED;
      if (field === "ticket_status") actionType = ACTION_TYPE.STATUS_CHANGED;
      if (field === "ticket_priority")
        actionType = ACTION_TYPE.PRIORITY_CHANGED;
      if (field === "assigned_to_user_id")
        actionType = newTicket[field]
          ? ACTION_TYPE.ASSIGNED
          : ACTION_TYPE.UNASSIGNED;
      if (field === "resolution_note") actionType = ACTION_TYPE.RESOLVED;

      await db.TicketHistory.create({
        ticket_id: newTicket.id,
        action_type: actionType,
        field_changed: field,
        old_value: String(oldTicket[field] ?? ""),
        new_value: String(newTicket[field] ?? ""),
        previous_status:
          field === "ticket_status" ? oldTicket[field] : undefined,
        new_status: field === "ticket_status" ? newTicket[field] : undefined,
        previous_priority:
          field === "ticket_priority" ? oldTicket[field] : undefined,
        new_priority:
          field === "ticket_priority" ? newTicket[field] : undefined,
        previous_assigned_to:
          field === "assigned_to_user_id" ? oldTicket[field] : undefined,
        new_assigned_to:
          field === "assigned_to_user_id" ? newTicket[field] : undefined,
        change_note: null,
        created_by: changedBy,
      });
    }
  }
};
