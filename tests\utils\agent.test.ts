import {
  isAgent,
  canViewInternalNotes,
  canCreateInternalNotes,
} from "../../src/utils/common";

// Mock the getUser function
jest.mock("../../src/utils/common", () => {
  const originalModule = jest.requireActual("../../src/utils/common");
  return {
    ...originalModule,
    getUser: jest.fn(),
    getUserAllRoles: jest.fn(),
    isDefaultAccess: jest.fn(),
  };
});

const {
  getUser,
  getUserAllRoles,
  isDefaultAccess,
} = require("../../src/utils/common");

describe("Agent Identification Functions", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global as any).config = {
      ORGANIZATION_ID: "b7ccd39a-23e9-49e6-8831-a3597a335bb1",
      API_BASE_URL: "http://localhost:8030/uploads",
      KEYCLOAK_SUPER_ADMIN_ROLE: "super_admin",
      KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION: "role_super_admin",
    };
  });

  describe("isAgent", () => {
    it("should return true for super admin users", async () => {
      isDefaultAccess.mockResolvedValue(true);

      const result = await isAgent(1);

      expect(result).toBe(true);
      expect(isDefaultAccess).toHaveBeenCalledWith(1);
    });

    it("should return true for users from the agent organization", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue({
        id: 2,
        organization_id: "b7ccd39a-23e9-49e6-8831-a3597a335bb1",
        name: "Agent User",
      });

      const result = await isAgent(2);

      expect(result).toBe(true);
      expect(getUser).toHaveBeenCalledWith(2, false);
    });

    it("should return false for users from different organization", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue({
        id: 3,
        organization_id: "different-org-id",
        name: "Regular User",
      });

      const result = await isAgent(3);

      expect(result).toBe(false);
    });

    it("should return false for non-existent users", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue(null);

      const result = await isAgent(999);

      expect(result).toBe(false);
    });

    it("should return false when ORGANIZATION_ID is not configured", async () => {
      (global as any).config = {};
      isDefaultAccess.mockResolvedValue(false);

      const result = await isAgent(1);

      expect(result).toBe(false);
    });

    it("should return false when userId is not provided", async () => {
      const result = await isAgent(null);

      expect(result).toBe(false);
    });
  });

  describe("canViewInternalNotes", () => {
    it("should return true for agents", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue({
        id: 2,
        organization_id: "b7ccd39a-23e9-49e6-8831-a3597a335bb1",
        name: "Agent User",
      });

      const result = await canViewInternalNotes(2);

      expect(result).toBe(true);
    });

    it("should return false for non-agents", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue({
        id: 3,
        organization_id: "different-org-id",
        name: "Regular User",
      });

      const result = await canViewInternalNotes(3);

      expect(result).toBe(false);
    });
  });

  describe("canCreateInternalNotes", () => {
    it("should return true for agents", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue({
        id: 2,
        organization_id: "b7ccd39a-23e9-49e6-8831-a3597a335bb1",
        name: "Agent User",
      });

      const result = await canCreateInternalNotes(2);

      expect(result).toBe(true);
    });

    it("should return false for non-agents", async () => {
      isDefaultAccess.mockResolvedValue(false);
      getUser.mockResolvedValue({
        id: 3,
        organization_id: "different-org-id",
        name: "Regular User",
      });

      const result = await canCreateInternalNotes(3);

      expect(result).toBe(false);
    });
  });
});
