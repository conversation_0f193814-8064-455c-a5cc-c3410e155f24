<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Message - {{ticket_number}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.6;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .logo-container {
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 120px;
            max-height: 60px;
            border-radius: 4px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #333333;
        }
        .message-info {
            background-color: #f8f9fa;
            border-left: 4px solid #9C27B0;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .message-info h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            color: #333333;
            flex: 1;
        }
        .message-bubble {
            background-color: #fff;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        .message-bubble::before {
            content: '';
            position: absolute;
            top: -10px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #e9ecef;
        }
        .message-bubble::after {
            content: '';
            position: absolute;
            top: -8px;
            left: 21px;
            width: 0;
            height: 0;
            border-left: 9px solid transparent;
            border-right: 9px solid transparent;
            border-bottom: 9px solid #fff;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .message-from {
            font-weight: 600;
            color: #9C27B0;
            font-size: 16px;
        }
        .message-time {
            color: #6c757d;
            font-size: 14px;
        }
        .message-content {
            color: #333333;
            line-height: 1.6;
            font-size: 15px;
        }
        .reply-section {
            background-color: #f3e5f5;
            border: 1px solid #ce93d8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }
        .reply-section h4 {
            margin: 0 0 10px 0;
            color: #7b1fa2;
        }
        .reply-button {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .reply-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #9C27B0;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .notification-badge {
            display: inline-block;
            background-color: #ff4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            line-height: 20px;
            margin-left: 5px;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            .message-header {
                flex-direction: column;
                align-items: flex-start;
            }
            .message-time {
                margin-top: 5px;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            {{#if ORGANIZATION_LOGO}}
            <div class="logo-container">
                <img src="{{ORGANIZATION_LOGO}}" alt="{{ORGANIZATION_NAME}}" />
            </div>
            {{/if}}
            <h1>💬 New Message</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello <strong>{{submitter_name}}</strong>,
            </div>

            <p>You have received a new message on your support ticket. Here are the details:</p>

            <div class="message-info">
                <h3>📋 Ticket Information</h3>
                <div class="info-row">
                    <div class="info-label">Ticket Number:</div>
                    <div class="info-value"><strong>{{ticket_number}}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Subject:</div>
                    <div class="info-value">{{subject}}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Message Time:</div>
                    <div class="info-value">{{CURRENT_DATE}}</div>
                </div>
            </div>

            <div class="message-bubble">
                <div class="message-header">
                    <div class="message-from">
                        {{#if message_from}}
                            {{message_from}}
                        {{else}}
                            Support Team
                        {{/if}}
                    </div>
                    <div class="message-time">{{CURRENT_TIME}}</div>
                </div>
                <div class="message-content">
                    {{#if message_text}}
                        {{message_text}}
                    {{else}}
                        A new message has been added to your support ticket. Please check your support portal for the complete message.
                    {{/if}}
                </div>
            </div>

            <div class="reply-section">
                <h4>💭 Want to respond?</h4>
                <p>You can reply directly to this email and your message will be added to the ticket conversation.</p>
                <a href="#" class="reply-button">Reply to Message</a>
                <p style="font-size: 14px; color: #6c757d; margin-top: 15px;">
                    Or visit your support portal to view the complete conversation history.
                </p>
            </div>

            <p style="margin-top: 30px;">
                <strong>📱 Stay Updated:</strong> You'll receive email notifications for all new messages and status updates on this ticket.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ORGANIZATION_NAME}} Support Team</strong></p>
            <p>This is an automated message. Please do not reply directly to this email.</p>
            <p>© {{CURRENT_YEAR}} {{ORGANIZATION_NAME}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
