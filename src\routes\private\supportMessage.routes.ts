import express from "express";
import uploadService from "../../helper/upload.service";
import supportMessageController from "../../controller/supportMessage.controller";
import {
  addMessageValidation,
  getTicketMessagesValidation,
} from "../../validators/supportMessage.validator";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "../../helper/common";

// Configure S3 Upload for Message Attachments
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  SUPPORT_FILE_UPLOAD_CONSTANT.MESSAGE_ATTACHMENT.folder
);

const router = express.Router();

/**
 * @swagger
 * /api/v1/private/messages/ticket/{ticket_id}:
 *   get:
 *     tags:
 *       - Support Messages
 *     summary: Get messages for a specific ticket
 *     description: Retrieve all messages for a ticket with optional pagination and filters
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: ticket_id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *       - name: page
 *         in: query
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - name: limit
 *         in: query
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of messages per page
 *       - name: include_private
 *         in: query
 *         schema:
 *           type: boolean
 *         description: Include private messages (agents only can see internal notes)
 *     responses:
 *       200:
 *         description: Messages retrieved successfully
 *       401:
 *         description: Unauthorized access
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Internal server error
 *   post:
 *     tags:
 *       - Support Messages
 *     summary: Add message to ticket
 *     description: |
 *       Add a new message to a specific ticket with optional file attachment.
 *
 *       **Message Types:**
 *       - `USER`: Regular user message (default)
 *       - `AGENT`: Agent response message
 *       - `SYSTEM`: System-generated message
 *       - `INTERNAL_NOTE`: Internal note (agents only) - automatically sets is_private=true
 *
 *       **Agent Permissions:**
 *       - Only users from the organization specified in ORGANIZATION_ID config can create internal notes
 *       - Internal notes are only visible to agents and admins
 *       - Internal notes do not trigger email notifications to customers
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: ticket_id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - message_text
 *             properties:
 *               message_text:
 *                 type: string
 *                 maxLength: 5000
 *                 description: Message content
 *                 example: "I need help with this issue"
 *               message_type:
 *                 type: string
 *                 enum: [USER, AGENT, SYSTEM, INTERNAL_NOTE, TICKET_COMMENT]
 *                 default: USER
 *                 description: |
 *                   Type of message:
 *                   - USER: Regular user message
 *                   - AGENT: Agent response
 *                   - SYSTEM: System message
 *                   - INTERNAL_NOTE: Internal note (agents only)
 *                   - TICKET_COMMENT: Ticket comment
 *               is_private:
 *                 type: boolean
 *                 default: false
 *                 description: |
 *                   Whether message is private.
 *                   Note: Automatically set to true for INTERNAL_NOTE message type
 *               attachment:
 *                 type: string
 *                 format: binary
 *                 description: Optional file attachment (max 50MB)
 *     responses:
 *       201:
 *         description: Message added successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized access
 *       403:
 *         description: Forbidden - Only agents can create internal notes
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Internal server error
 */

router.get(
  "/ticket/:ticket_id",
  getTicketMessagesValidation(),
  supportMessageController.getTicketMessages
);

// Add message with file upload (standardized with recipe pattern)
router.post(
  "/ticket/:ticket_id",
  multerS3Upload?.s3UploadFieldsMiddleware?.([
    { name: "attachment", maxCount: 1 },
  ]) || ((req: any, res: any, next: any) => next()),
  addMessageValidation(),
  supportMessageController.addMessage
);

export default router;
