import request from 'supertest';
import { StatusCodes } from 'http-status-codes';

// Mock the dependencies
jest.mock('../../src/models', () => ({
  db: {
    Ticket: {
      findByPk: jest.fn(),
    },
    TicketMessage: {
      create: jest.fn(),
    },
    TicketHistory: {
      create: jest.fn(),
    },
    Op: {
      notIn: 'notIn',
      ne: 'ne',
    },
  },
}));

jest.mock('../../src/utils/common', () => ({
  canCreateInternalNotes: jest.fn(),
  canViewInternalNotes: jest.fn(),
  getUser: jest.fn(),
}));

jest.mock('../../src/services/notification.service', () => ({
  sendSupportTicketNotification: jest.fn(),
}));

import { db } from '../../src/models';
import { canCreateInternalNotes, canViewInternalNotes, getUser } from '../../src/utils/common';
import supportMessageController from '../../src/controller/supportMessage.controller';
import { MESSAGE_TYPE } from '../../src/helper/constant';

describe('Support Message Controller - Internal Notes', () => {
  let mockReq: any;
  let mockRes: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockReq = {
      params: { ticket_id: '1' },
      body: {},
      user: { id: 1 },
      files: null,
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      __: jest.fn((key: string) => key),
    };
  });

  describe('addMessage - Internal Notes', () => {
    beforeEach(() => {
      db.Ticket.findByPk.mockResolvedValue({
        id: 1,
        ticket_number: 'TKT-001',
        ticket_status: 'OPEN',
        ticket_owner_email: '<EMAIL>',
        ticket_owner_name: 'Test User',
        ticket_title: 'Test Ticket',
      });

      db.TicketMessage.create.mockResolvedValue({
        id: 1,
        ticket_id: 1,
        message_text: 'Test internal note',
        message_type: MESSAGE_TYPE.INTERNAL_NOTE,
        is_private: true,
        created_by: 1,
        toJSON: () => ({
          id: 1,
          ticket_id: 1,
          message_text: 'Test internal note',
          message_type: MESSAGE_TYPE.INTERNAL_NOTE,
          is_private: true,
          created_by: 1,
        }),
      });

      getUser.mockResolvedValue({
        id: 1,
        name: 'Agent User',
      });
    });

    it('should allow agents to create internal notes', async () => {
      canCreateInternalNotes.mockResolvedValue(true);
      
      mockReq.body = {
        message_text: 'This is an internal note',
        message_type: MESSAGE_TYPE.INTERNAL_NOTE,
      };

      await supportMessageController.addMessage(mockReq, mockRes);

      expect(canCreateInternalNotes).toHaveBeenCalledWith(1);
      expect(db.TicketMessage.create).toHaveBeenCalledWith({
        ticket_id: 1,
        message_text: 'This is an internal note',
        message_type: MESSAGE_TYPE.INTERNAL_NOTE,
        is_private: true, // Should be automatically set to true
        attachment_id: null,
        created_by: 1,
      });
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.CREATED);
    });

    it('should deny non-agents from creating internal notes', async () => {
      canCreateInternalNotes.mockResolvedValue(false);
      
      mockReq.body = {
        message_text: 'This should fail',
        message_type: MESSAGE_TYPE.INTERNAL_NOTE,
      };

      await supportMessageController.addMessage(mockReq, mockRes);

      expect(canCreateInternalNotes).toHaveBeenCalledWith(1);
      expect(db.TicketMessage.create).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.FORBIDDEN);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'INTERNAL_NOTE_ACCESS_DENIED',
      });
    });

    it('should automatically set is_private to true for internal notes', async () => {
      canCreateInternalNotes.mockResolvedValue(true);
      
      mockReq.body = {
        message_text: 'Internal note',
        message_type: MESSAGE_TYPE.INTERNAL_NOTE,
        is_private: false, // This should be overridden
      };

      await supportMessageController.addMessage(mockReq, mockRes);

      expect(db.TicketMessage.create).toHaveBeenCalledWith(
        expect.objectContaining({
          is_private: true, // Should be forced to true
        })
      );
    });

    it('should create history entry for internal notes', async () => {
      canCreateInternalNotes.mockResolvedValue(true);
      
      mockReq.body = {
        message_text: 'Internal note for history',
        message_type: MESSAGE_TYPE.INTERNAL_NOTE,
      };

      await supportMessageController.addMessage(mockReq, mockRes);

      expect(db.TicketHistory.create).toHaveBeenCalledWith({
        ticket_id: 1,
        action_type: 'INTERNAL_NOTE_ADDED',
        previous_status: 'OPEN',
        new_status: 'OPEN',
        change_note: 'Internal note added by agent: Internal note for history',
        created_by: 1,
      });
    });

    it('should allow regular messages for all users', async () => {
      mockReq.body = {
        message_text: 'Regular user message',
        message_type: MESSAGE_TYPE.USER,
      };

      await supportMessageController.addMessage(mockReq, mockRes);

      // Should not check for internal note permissions
      expect(canCreateInternalNotes).not.toHaveBeenCalled();
      expect(db.TicketMessage.create).toHaveBeenCalledWith({
        ticket_id: 1,
        message_text: 'Regular user message',
        message_type: MESSAGE_TYPE.USER,
        is_private: false,
        attachment_id: null,
        created_by: 1,
      });
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.CREATED);
    });
  });
});
