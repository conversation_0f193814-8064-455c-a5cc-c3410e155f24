'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔧 Adding time tracking columns without indexes to avoid "Too many keys" error...');
    
    // Add columns to mo_support_configs table
    const configColumns = [
      {
        name: 'sla_enabled',
        definition: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: 'Enable/disable SLA calculation for this organization'
        }
      },
      {
        name: 'enable_time_tracking',
        definition: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: 'Enable time tracking for tickets'
        }
      },
      {
        name: 'allow_agent_time_updates',
        definition: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: 'Allow agents to update time estimates and actual hours'
        }
      }
    ];

    for (const column of configColumns) {
      try {
        await queryInterface.addColumn('mo_support_configs', column.name, column.definition);
        console.log(`✅ Added column: mo_support_configs.${column.name}`);
      } catch (error) {
        console.log(`⚠️  Column mo_support_configs.${column.name} may already exist:`, error.message);
      }
    }

    // Add columns to mo_support_tickets table
    const ticketColumns = [
      {
        name: 'estimated_hours',
        definition: {
          type: Sequelize.DECIMAL(8, 2),
          allowNull: true,
          comment: 'Estimated hours to complete the ticket'
        }
      },
      {
        name: 'actual_hours',
        definition: {
          type: Sequelize.DECIMAL(8, 2),
          allowNull: true,
          comment: 'Actual hours spent on the ticket'
        }
      },
      {
        name: 'work_started_at',
        definition: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: 'When work actually started on the ticket'
        }
      },
      {
        name: 'work_completed_at',
        definition: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: 'When work was completed on the ticket'
        }
      },
      {
        name: 'time_logged_by_user_id',
        definition: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'User who logged the time tracking information'
        }
      }
    ];

    for (const column of ticketColumns) {
      try {
        await queryInterface.addColumn('mo_support_tickets', column.name, column.definition);
        console.log(`✅ Added column: mo_support_tickets.${column.name}`);
      } catch (error) {
        console.log(`⚠️  Column mo_support_tickets.${column.name} may already exist:`, error.message);
      }
    }

    console.log('🎉 Time tracking columns migration completed!');
  },

  async down(queryInterface) {
    console.log('🔄 Removing time tracking columns...');
    
    const configColumns = ['sla_enabled', 'enable_time_tracking', 'allow_agent_time_updates'];
    const ticketColumns = ['estimated_hours', 'actual_hours', 'work_started_at', 'work_completed_at', 'time_logged_by_user_id'];

    // Remove from mo_support_configs
    for (const column of configColumns) {
      try {
        await queryInterface.removeColumn('mo_support_configs', column);
        console.log(`✅ Removed column: mo_support_configs.${column}`);
      } catch (error) {
        console.log(`⚠️  Error removing mo_support_configs.${column}:`, error.message);
      }
    }

    // Remove from mo_support_tickets
    for (const column of ticketColumns) {
      try {
        await queryInterface.removeColumn('mo_support_tickets', column);
        console.log(`✅ Removed column: mo_support_tickets.${column}`);
      } catch (error) {
        console.log(`⚠️  Error removing mo_support_tickets.${column}:`, error.message);
      }
    }

    console.log('🎉 Time tracking columns rollback completed!');
  }
};
