<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Ticket Created - {{ticket_number}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.6;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .logo-container {
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 120px;
            max-height: 60px;
            border-radius: 4px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #333333;
        }
        .ticket-info {
            background-color: #f8f9fa;
            border-left: 4px solid #4CAF50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .ticket-info h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            color: #333333;
            flex: 1;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            background-color: #28a745;
            color: white;
        }
        .priority-high { background-color: #dc3545; }
        .priority-medium { background-color: #ffc107; color: #333; }
        .priority-low { background-color: #6c757d; }
        .priority-urgent { background-color: #fd7e14; }
        .next-steps {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .next-steps h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #4CAF50;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .support-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            {{#if organization_logo}}
            <div class="logo-container">
                <img src="{{organization_logo}}" alt="{{organization_name}}" />
            </div>
            {{/if}}
            <h1>🎫 Support Ticket Created</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello <strong>{{submitter_name}}</strong>,
            </div>

            <p>Thank you for contacting our support team. Your support ticket has been successfully created and assigned the following details:</p>

            <div class="ticket-info">
                <h3>📋 Ticket Information</h3>
                <div class="info-row">
                    <div class="info-label">Ticket Number:</div>
                    <div class="info-value"><strong>{{ticket_number}}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Subject:</div>
                    <div class="info-value">{{subject}}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Status:</div>
                    <div class="info-value">
                        <span class="status-badge">Open</span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Created:</div>
                    <div class="info-value">{{CURRENT_DATE}}</div>
                </div>
                {{#if organization_id}}
                <div class="info-row">
                    <div class="info-label">Organization:</div>
                    <div class="info-value">{{organization_id}}</div>
                </div>
                {{/if}}
            </div>

            <div class="next-steps">
                <h4>🚀 What happens next?</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Our support team will review your ticket within <strong>24 hours</strong></li>
                    <li>You'll receive email updates when your ticket status changes</li>
                    <li>A support agent will be assigned to help resolve your issue</li>
                    <li>You can reply to this email to add additional information</li>
                </ul>
            </div>

            <div class="support-info">
                <strong>💡 Need immediate assistance?</strong><br>
                For urgent issues, please contact our support team directly or check our knowledge base for common solutions.
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ORGANIZATION_NAME}} Support Team</strong></p>
            <p>This is an automated message. Please do not reply directly to this email.</p>
            <p>© {{CURRENT_YEAR}} {{ORGANIZATION_NAME}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
