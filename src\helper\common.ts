import fs from "fs";
import { createHash } from "crypto";
import * as path from "path";
import { QueryTypes } from "sequelize";
import { User } from "../models/User";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "./constant";

/**
 * Read HTML file for email templates
 * @param filePath - Path to HTML file
 * @param callback - Callback function
 */
export const readHTMLFile = function (filePath: any, callback: any) {
  fs.readFile(filePath, "utf-8", function (err, data) {
    if (err) {
      console.log("Error reading HTML file:", err);
      throw err;
    } else {
      callback(null, data);
    }
  });
};

/**
 * Get pagination parameters
 * @param page - Page number
 * @param size - Page size
 * @returns Pagination object with limit and offset
 */
export const getPagination = (page: number, size: number) => {
  const limit = size;
  const Page = page || 1;
  const offset = (Page - 1) * limit;
  return { limit, offset };
};

/**
 * Get paginated response metadata
 * @param pageSize - Items per page
 * @param pageNumber - Current page number
 * @param total - Total items count
 * @returns Pagination metadata
 */
export const getPaginatedItems = (
  pageSize: number,
  pageNumber: number,
  total: number
) => {
  return {
    pageNumber: pageNumber,
    per_page: pageSize,
    total: total,
    total_pages: Math.ceil(total / pageSize),
  };
};

/**
 * Get user by ID (internal or Keycloak)
 * @param id - User ID
 * @param isAuth - Whether to use Keycloak auth ID
 * @returns User object or null
 */
export const getUser = async (id: any, isAuth: boolean = false) => {
  try {
    const user = await User.findOne({
      where: isAuth ? { keycloak_auth_id: id } : { id: id },
      raw: true,
    });
    return user;
  } catch (error) {
    console.log("Error getting user:", error);
    return null;
  }
};

/**
 * Get user full name by user ID
 * @param user_id - User ID
 * @returns Promise<string> - User full name or fallback
 */
export const getUserFullName = async (user_id: any): Promise<string> => {
  try {
    if (!user_id) return "Unknown User";

    const user = await getUser(user_id, false);
    if (!user) return "Unknown User";

    const nameComponents = [];
    if (user.user_first_name) nameComponents.push(user.user_first_name);
    if (user.user_middle_name) nameComponents.push(user.user_middle_name);
    if (user.user_last_name) nameComponents.push(user.user_last_name);

    // If no name parts found, use email as fallback
    if (nameComponents.length === 0 && user.user_email) {
      return user.user_email.split("@")[0];
    }

    return nameComponents.length > 0
      ? nameComponents.join(" ")
      : "Unknown User";
  } catch (error) {
    console.log("Error getting user full name:", error);
    return "Unknown User";
  }
};

/**
 * Generate file hash to prevent duplicates
 * @param file - File object
 * @param mimeType - Optional MIME type
 * @returns Hash object with status and hash
 */
export const getHash = function (file: any, mimeType?: string) {
  try {
    if (!file) {
      return { status: false, hash: null };
    }

    let content = "";
    if (file.buffer) {
      content = file.buffer.toString("binary");
    } else if (file.originalname) {
      content = file.originalname + (file.size || 0).toString();
    } else {
      content = JSON.stringify(file);
    }

    const hash = createHash("md5")
      .update(content + Date.now().toString())
      .digest("hex");

    return { status: true, hash: hash };
  } catch (error) {
    console.log("Error generating hash:", error);
    return { status: false, hash: null };
  }
};

/**
 * Get MIME type from file extension
 * @param ext - File extension
 * @returns MIME type
 */
export const getMimeTypeFromExtension = (ext: string): string => {
  const mimeTypes: { [key: string]: string } = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".webp": "image/webp",
    ".bmp": "image/bmp",
    ".tiff": "image/tiff",
    ".svg": "image/svg+xml",
    ".pdf": "application/pdf",
    ".doc": "application/msword",
    ".docx":
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".xls": "application/vnd.ms-excel",
    ".xlsx":
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".ppt": "application/vnd.ms-powerpoint",
    ".pptx":
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ".txt": "text/plain",
    ".csv": "text/csv",
    ".json": "application/json",
    ".xml": "application/xml",
    ".mp4": "video/mp4",
    ".avi": "video/avi",
    ".mov": "video/mov",
    ".wmv": "video/wmv",
    ".webm": "video/webm",
    ".mkv": "video/mkv",
    ".mp3": "audio/mp3",
    ".wav": "audio/wav",
    ".ogg": "audio/ogg",
    ".m4a": "audio/m4a",
    ".aac": "audio/aac",
    ".flac": "audio/flac",
    ".zip": "application/zip",
    ".rar": "application/x-rar-compressed",
    ".7z": "application/x-7z-compressed",
    ".tar": "application/x-tar",
    ".gz": "application/gzip",
  };

  return mimeTypes[ext.toLowerCase()] || "application/octet-stream";
};

/**
 * Generate image URL from item_location
 * @param itemLocation - The S3 path stored in database
 * @returns Full URL to access the image via file serving endpoint
 */
export const getImageUrl = (itemLocation: string | null): string | null => {
  if (!itemLocation) return null;

  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    return `${baseUrl}${itemLocation}`;
  } else {
    // For development or when API_BASE_URL is just the base domain
    const fallbackUrl = "https://staging.namastevillage.theeasyaccess.com";
    return `${fallbackUrl}/backend-api/v1/public/user/get-file?location=${itemLocation}`;
  }
};

/**
 * Check if file is image based on MIME type
 * @param mimeType - File MIME type
 * @returns boolean
 */
export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith("image/");
};

/**
 * Check if file is document based on MIME type
 * @param mimeType - File MIME type
 * @returns boolean
 */
export const isDocumentFile = (mimeType: string): boolean => {
  const docTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
    "application/json",
    "application/xml",
  ];
  return docTypes.includes(mimeType);
};

/**
 * Check if file is video based on MIME type
 * @param mimeType - File MIME type
 * @returns boolean
 */
export const isVideoFile = (mimeType: string): boolean => {
  return mimeType.startsWith("video/");
};

/**
 * Check if file is audio based on MIME type
 * @param mimeType - File MIME type
 * @returns boolean
 */
export const isAudioFile = (mimeType: string): boolean => {
  return mimeType.startsWith("audio/");
};

/**
 * Format file size in human readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Validate file size
 * @param fileSize - File size in bytes
 * @param maxSize - Maximum allowed size in bytes
 * @returns boolean
 */
export const isValidFileSize = (fileSize: number, maxSize: number): boolean => {
  return fileSize <= maxSize;
};

/**
 * Validate file type
 * @param mimeType - File MIME type
 * @param allowedTypes - Array of allowed MIME types
 * @returns boolean
 */
export const isValidFileType = (
  mimeType: string,
  allowedTypes: string[]
): boolean => {
  return allowedTypes.includes(mimeType);
};

/**
 * Generate safe filename
 * @param originalName - Original filename
 * @param maxLength - Maximum filename length
 * @returns Safe filename
 */
export const generateSafeFilename = (
  originalName: string,
  maxLength: number = 255
): string => {
  // Remove unsafe characters
  const safeName = originalName.replace(/[^\w\s.-]/g, "");

  // Limit length
  if (safeName.length > maxLength) {
    const ext = path.extname(safeName);
    const nameWithoutExt = safeName.substring(0, maxLength - ext.length);
    return nameWithoutExt + ext;
  }

  return safeName;
};

/**
 * Create notification data
 * @param data - Notification data
 * @returns Notification object
 */
export const createNotification = async (data: any) => {
  try {
    // This would integrate with your notification system
    // For now, just log the notification
    console.log("Creating notification:", data);
    return { status: true, data: data };
  } catch (error) {
    console.log("Error creating notification:", error);
    return { status: false, error: error };
  }
};

/**
 * Get organization name by ID
 * @param orgId - Organization ID
 * @returns Organization name
 */
export const getOrgName = async (orgId: any) => {
  try {
    if (!orgId) return null;

    // This would query your organization table
    // For now, return the orgId as string
    return orgId.toString();
  } catch (error) {
    console.log("Error getting organization name:", error);
    return null;
  }
};

/**
 * Reading file content
 * @param filePath - File path
 * @returns File content or null
 */
export const readingFile = (filePath: string) => {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    return fs.readFileSync(filePath, "utf8");
  } catch (error) {
    console.log("Error reading file:", error);
    return null;
  }
};

/**
 * Determine the correct folder path based on field name and context
 * @param fieldName - The form field name
 * @param fileName - The clean file name
 * @param req - The request object to get context
 * @param ticketId - Optional ticket ID
 * @param messageId - Optional message ID
 * @returns The folder path for the file
 */
export const determineFolderPath = (
  fieldName: string,
  fileName: string,
  req: any,
  ticketId?: any,
  messageId?: any
): string => {
  const isSystemDefault = !req.user?.organization_id;
  const orgName = isSystemDefault ? null : req.user?.organization_id;

  // Map field names to upload constants
  switch (fieldName) {
    case "ticketAttachment":
    case "ticketAttachments":
      return SUPPORT_FILE_UPLOAD_CONSTANT.TICKET_ATTACHMENT.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    case "messageAttachment":
    case "messageAttachments":
      return SUPPORT_FILE_UPLOAD_CONSTANT.MESSAGE_ATTACHMENT.destinationPath(
        orgName,
        ticketId,
        messageId,
        fileName
      );
    case "supportDocument":
    case "supportDocuments":
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_DOCUMENT.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    case "supportImage":
    case "supportImages":
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_IMAGE.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    case "supportVideo":
    case "supportVideos":
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_VIDEO.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    case "supportAudio":
    case "supportAudios":
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_AUDIO.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    case "supportScreenshot":
    case "supportScreenshots":
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_SCREENSHOT.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    case "supportLog":
    case "supportLogs":
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_LOG.destinationPath(
        orgName,
        ticketId,
        fileName
      );
    default:
      return SUPPORT_FILE_UPLOAD_CONSTANT.SUPPORT_MISC.destinationPath(
        orgName,
        ticketId,
        fileName
      );
  }
};

/**
 * Auto-detect field name based on file MIME type
 * @param mimeType - The file MIME type
 * @param originalFieldName - The original field name from the form
 * @returns Enhanced field name for proper routing
 */
export const enhanceFieldNameByMimeType = (
  mimeType: string,
  originalFieldName: string
): string => {
  // If field name already specifies the type, use it as-is
  if (
    originalFieldName.includes("Document") ||
    originalFieldName.includes("Image") ||
    originalFieldName.includes("Video") ||
    originalFieldName.includes("Audio") ||
    originalFieldName.includes("Screenshot") ||
    originalFieldName.includes("Log")
  ) {
    return originalFieldName;
  }

  // Auto-detect based on MIME type for generic field names
  if (
    originalFieldName.startsWith("support") ||
    originalFieldName.startsWith("ticket")
  ) {
    if (mimeType.startsWith("image/")) {
      return "supportImage";
    } else if (mimeType.startsWith("video/")) {
      return "supportVideo";
    } else if (mimeType.startsWith("audio/")) {
      return "supportAudio";
    } else if (isDocumentFile(mimeType)) {
      return "supportDocument";
    }
  }

  // Return original field name if no enhancement needed
  return originalFieldName;
};

// Export all constants for easy access
export * from "./constant";
