// Test setup file
import dotenv from "dotenv";

// Load environment variables for testing
dotenv.config();

// Set test environment
process.env.NODE_ENV = "test";

// Mock global config for testing
(global as any).config = {
  ORGANIZATION_ID: "b7ccd39a-23e9-49e6-8831-a3597a335bb1",
  API_BASE_URL: "http://localhost:8030/uploads",
  KEYCLOAK_SUPER_ADMIN_ROLE: "super_admin",
  KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION: "role_super_admin",
};

// Mock console methods to reduce noise in tests
(global as any).console = {
  ...console,
  log: () => {},
  debug: () => {},
  info: () => {},
  warn: () => {},
  error: () => {},
};
