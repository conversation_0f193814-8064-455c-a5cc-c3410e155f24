<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Assigned - {{ticket_number}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.6;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .logo-container {
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 120px;
            max-height: 60px;
            border-radius: 4px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #333333;
        }
        .assignment-info {
            background-color: #f8f9fa;
            border-left: 4px solid #FF9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .assignment-info h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .agent-card {
            background-color: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .agent-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
            font-weight: bold;
        }
        .agent-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .agent-title {
            color: #6c757d;
            font-size: 14px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            color: #333333;
            flex: 1;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            background-color: #FF9800;
            color: white;
        }
        .next-steps {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .next-steps h4 {
            margin: 0 0 10px 0;
            color: #e65100;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #FF9800;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .contact-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6cb;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            {{#if ORGANIZATION_LOGO}}
            <div class="logo-container">
                <img src="{{ORGANIZATION_LOGO}}" alt="{{ORGANIZATION_NAME}}" />
            </div>
            {{/if}}
            <h1>👨‍💻 Ticket Assigned</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello <strong>{{submitter_name}}</strong>,
            </div>

            <p>Great news! Your support ticket has been assigned to one of our experienced support specialists who will personally handle your request.</p>

            <div class="assignment-info">
                <h3>📋 Assignment Details</h3>
                <div class="info-row">
                    <div class="info-label">Ticket Number:</div>
                    <div class="info-value"><strong>{{ticket_number}}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Subject:</div>
                    <div class="info-value">{{subject}}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Status:</div>
                    <div class="info-value">
                        <span class="status-badge">Assigned</span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Assigned Date:</div>
                    <div class="info-value">{{CURRENT_DATE}}</div>
                </div>
            </div>

            <div class="agent-card">
                <div class="agent-avatar">
                    {{#if assigned_to}}
                        {{substring assigned_to 0 1}}
                    {{else}}
                        S
                    {{/if}}
                </div>
                <div class="agent-name">
                    {{#if assigned_to}}
                        {{assigned_to}}
                    {{else}}
                        Support Specialist
                    {{/if}}
                </div>
                <div class="agent-title">Support Team Member</div>
            </div>

            <div class="next-steps">
                <h4>🚀 What happens next?</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Your assigned specialist will review your ticket details</li>
                    <li>You can expect an initial response within <strong>24 hours</strong></li>
                    <li>They will work with you to resolve your issue efficiently</li>
                    <li>You'll receive updates as progress is made on your ticket</li>
                </ul>
            </div>

            <div class="contact-info">
                <strong>💬 Need to add more information?</strong><br>
                Simply reply to this email and your message will be added to the ticket. Your assigned specialist will see all updates immediately.
            </div>

            <p style="margin-top: 30px;">
                We're committed to providing you with excellent support. Thank you for your patience as we work to resolve your issue.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ORGANIZATION_NAME}} Support Team</strong></p>
            <p>This is an automated message. Please do not reply directly to this email.</p>
            <p>© {{CURRENT_YEAR}} {{ORGANIZATION_NAME}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
