<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Status Updated - {{ticket_number}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.6;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .logo-container {
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 120px;
            max-height: 60px;
            border-radius: 4px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #333333;
        }
        .status-update {
            background-color: #f8f9fa;
            border-left: 4px solid #2196F3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .status-update h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .status-transition {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: capitalize;
            margin: 0 10px;
        }
        .status-open { background-color: #28a745; color: white; }
        .status-assigned { background-color: #17a2b8; color: white; }
        .status-in_progress { background-color: #ffc107; color: #333; }
        .status-on_hold { background-color: #6c757d; color: white; }
        .status-resolved { background-color: #28a745; color: white; }
        .status-closed { background-color: #343a40; color: white; }
        .arrow {
            font-size: 20px;
            color: #6c757d;
            margin: 0 10px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            color: #333333;
            flex: 1;
        }
        .resolution-note {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .resolution-note h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }
        .next-steps {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .next-steps h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #2196F3;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            .status-transition {
                flex-direction: column;
            }
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            {{#if ORGANIZATION_LOGO}}
            <div class="logo-container">
                <img src="{{ORGANIZATION_LOGO}}" alt="{{ORGANIZATION_NAME}}" />
            </div>
            {{/if}}
            <h1>🔄 Ticket Status Updated</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello <strong>{{submitter_name}}</strong>,
            </div>

            <p>Great news! Your support ticket status has been updated. Here are the details:</p>

            <div class="status-update">
                <h3>📋 Ticket Information</h3>
                <div class="info-row">
                    <div class="info-label">Ticket Number:</div>
                    <div class="info-value"><strong>{{ticket_number}}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Subject:</div>
                    <div class="info-value">{{subject}}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Updated:</div>
                    <div class="info-value">{{CURRENT_DATE}}</div>
                </div>
            </div>

            <div class="status-transition">
                <span class="status-badge status-{{previous_status}}">{{previous_status}}</span>
                <span class="arrow">→</span>
                <span class="status-badge status-{{new_status}}">{{new_status}}</span>
            </div>

            {{#if resolution_note}}
            <div class="resolution-note">
                <h4>📝 Update Notes</h4>
                <p>{{resolution_note}}</p>
            </div>
            {{/if}}

            {{#if (eq new_status "in_progress")}}
            <div class="next-steps">
                <h4>🚀 What's happening now?</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Our support team is actively working on your issue</li>
                    <li>You'll receive updates as progress is made</li>
                    <li>Feel free to add any additional information by replying to this email</li>
                </ul>
            </div>
            {{/if}}

            {{#if (eq new_status "resolved")}}
            <div class="next-steps">
                <h4>✅ Issue Resolved!</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Your issue has been resolved by our support team</li>
                    <li>Please test the solution and confirm it works for you</li>
                    <li>If you're satisfied, no further action is needed</li>
                    <li>If you need additional help, simply reply to this email</li>
                </ul>
            </div>
            {{/if}}

            {{#if (eq new_status "assigned")}}
            <div class="next-steps">
                <h4>👨‍💻 Assigned to Support Agent</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Your ticket has been assigned to a support specialist</li>
                    <li>They will review your issue and provide assistance</li>
                    <li>You can expect an initial response within 24 hours</li>
                </ul>
            </div>
            {{/if}}

            <p style="margin-top: 30px;">
                <strong>Need to add more information?</strong> Simply reply to this email and your message will be added to the ticket.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ORGANIZATION_NAME}} Support Team</strong></p>
            <p>This is an automated message. Please do not reply directly to this email.</p>
            <p>© {{CURRENT_YEAR}} {{ORGANIZATION_NAME}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
