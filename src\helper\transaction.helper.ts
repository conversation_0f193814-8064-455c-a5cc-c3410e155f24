import { db, sequelize } from "../models";
import { MESSAGE_TYPE, TICKET_STATUS } from "./constant";

// Define transaction type based on sequelize instance
type SequelizeTransaction = Awaited<ReturnType<typeof sequelize.transaction>>;

/**
 * Enhanced transaction management utility for Customer Service module
 * Provides safe transaction handling with proper cleanup
 * Following the same patterns as the recipe module
 */
export class TransactionManager {
  private transaction: SequelizeTransaction | null = null;
  private isCommitted = false;
  private isRolledBack = false;

  /**
   * Start a new transaction
   */
  async start(): Promise<SequelizeTransaction> {
    if (this.transaction) {
      throw new Error("Transaction already started");
    }

    this.transaction = await sequelize.transaction();
    this.isCommitted = false;
    this.isRolledBack = false;
    return this.transaction;
  }

  /**
   * Get the current transaction
   */
  get(): SequelizeTransaction {
    if (!this.transaction) {
      throw new Error("Transaction not started");
    }
    return this.transaction;
  }

  /**
   * Commit the transaction
   */
  async commit(): Promise<void> {
    if (!this.transaction) {
      throw new Error("No transaction to commit");
    }

    if (this.isCommitted) {
      throw new Error("Transaction already committed");
    }

    if (this.isRolledBack) {
      throw new Error("Cannot commit a rolled back transaction");
    }

    await this.transaction.commit();
    this.isCommitted = true;
    this.transaction = null;
  }

  /**
   * Rollback the transaction
   */
  async rollback(): Promise<void> {
    if (!this.transaction) {
      console.warn("No transaction to rollback");
      return;
    }

    if (this.isCommitted) {
      throw new Error("Cannot rollback a committed transaction");
    }

    if (this.isRolledBack) {
      console.warn("Transaction already rolled back");
      return;
    }

    try {
      await this.transaction.rollback();
      this.isRolledBack = true;
      this.transaction = null;
    } catch (error) {
      console.error("Error during transaction rollback:", error);
      this.transaction = null;
      throw error;
    }
  }

  /**
   * Execute a function within a transaction with automatic cleanup
   */
  static async execute<T>(
    operation: (transaction: SequelizeTransaction) => Promise<T>
  ): Promise<T> {
    const manager = new TransactionManager();

    try {
      const transaction = await manager.start();
      const result = await operation(transaction);
      await manager.commit();
      return result;
    } catch (error) {
      await manager.rollback();
      throw error;
    }
  }

  /**
   * Check if transaction is active
   */
  isActive(): boolean {
    return this.transaction !== null && !this.isCommitted && !this.isRolledBack;
  }

  /**
   * Get transaction status
   */
  getStatus(): "none" | "active" | "committed" | "rolled_back" {
    if (!this.transaction && !this.isCommitted && !this.isRolledBack) {
      return "none";
    }
    if (this.isCommitted) {
      return "committed";
    }
    if (this.isRolledBack) {
      return "rolled_back";
    }
    return "active";
  }
}

/**
 * Customer Service specific transaction operations
 */
export class CustomerServiceTransactionHelper {
  /**
   * Create ticket with messages in a single transaction
   */
  static async createTicketWithMessage(
    ticketData: any,
    messageData: any
  ): Promise<{ ticket: any; message: any }> {
    return TransactionManager.execute(async (transaction) => {
      // Create ticket
      const ticket = await db.Ticket.create(ticketData, { transaction });

      // Create initial message
      const message = await db.TicketMessage.create(
        {
          ...messageData,
          ticket_id: ticket.id,
        },
        { transaction }
      );

      return { ticket, message };
    });
  }

  /**
   * Update ticket and create system message in a single transaction
   */
  static async updateTicketWithSystemMessage(
    ticketId: string,
    ticketUpdates: any,
    systemMessage: string,
    userId?: number
  ): Promise<{ ticket: any; message: any }> {
    return TransactionManager.execute(async (transaction) => {
      // Update ticket
      const ticket = await db.Ticket.findByPk(ticketId, { transaction });
      if (!ticket) {
        throw new Error("Ticket not found");
      }

      await ticket.update(
        {
          ...ticketUpdates,
          updated_by: userId,
        },
        { transaction }
      );

      // Create system message
      const message = await db.TicketMessage.create(
        {
          ticket_id: ticketId,
          sender_type: MESSAGE_TYPE.SYSTEM,
          sender_name: "System",
          message_content: systemMessage,
          message_visibility: "internal",
          is_ai_generated: false,
          marked_as_solution: false,
          solution_verified: false,
          organization_id: ticket.organization_id,
          created_by: userId,
        },
        { transaction }
      );

      return { ticket, message };
    });
  }

  /**
   * Escalate ticket with all related updates in a single transaction
   */
  static async escalateTicketTransaction(
    ticketId: string,
    escalationData: {
      reason: string;
      escalated_by_user_id?: number;
      assigned_to_user_id?: number;
    }
  ): Promise<{ ticket: any; escalationMessage: any; assignmentMessage?: any }> {
    return TransactionManager.execute(async (transaction) => {
      // Update ticket status
      const ticket = await db.Ticket.findByPk(ticketId, { transaction });
      if (!ticket) {
        throw new Error("Ticket not found");
      }

      await ticket.update(
        {
          ticket_status: "escalated",
          auto_escalated: !escalationData.escalated_by_user_id,
          escalation_reason: escalationData.reason,
          assigned_to_user_id: escalationData.assigned_to_user_id,
          assigned_by_user_id: escalationData.escalated_by_user_id,
          assigned_at: escalationData.assigned_to_user_id ? new Date() : null,
          updated_by: escalationData.escalated_by_user_id,
        },
        { transaction }
      );

      // Create escalation message
      const escalationMessage = await db.TicketMessage.create(
        {
          ticket_id: ticketId,
          sender_type: MESSAGE_TYPE.SYSTEM,
          sender_name: "System",
          message_content: `Ticket has been escalated. Reason: ${escalationData.reason}`,
          message_visibility: "internal",
          is_ai_generated: false,
          marked_as_solution: false,
          solution_verified: false,
          organization_id: ticket.organization_id,
          created_by: escalationData.escalated_by_user_id,
        },
        { transaction }
      );

      let assignmentMessage;
      if (escalationData.assigned_to_user_id) {
        // Create assignment message
        assignmentMessage = await db.TicketMessage.create(
          {
            ticket_id: ticketId,
            sender_type: MESSAGE_TYPE.SYSTEM,
            sender_name: "System",
            message_content:
              "Ticket has been assigned to an expert for resolution.",
            message_visibility: "public",
            is_ai_generated: false,
            marked_as_solution: false,
            solution_verified: false,
            organization_id: ticket.organization_id,
            created_by: escalationData.escalated_by_user_id,
          },
          { transaction }
        );
      }

      return { ticket, escalationMessage, assignmentMessage };
    });
  }

  /**
   * Resolve ticket with solution message in a single transaction
   */
  static async resolveTicketTransaction(
    ticketId: string,
    resolutionData: {
      resolution_summary: string;
      solution_message_id?: string;
      resolved_by_user_id: number;
    }
  ): Promise<{ ticket: any; resolutionMessage: any }> {
    return TransactionManager.execute(async (transaction) => {
      // Update ticket
      const ticket = await db.Ticket.findByPk(ticketId, { transaction });
      if (!ticket) {
        throw new Error("Ticket not found");
      }

      await ticket.update(
        {
          ticket_status: TICKET_STATUS.RESOLVED,
          resolved_at: new Date(),
          resolution_summary: resolutionData.resolution_summary,
          updated_by: resolutionData.resolved_by_user_id,
        },
        { transaction }
      );

      // Mark solution message if provided
      if (resolutionData.solution_message_id) {
        await db.TicketMessage.update(
          {
            marked_as_solution: true,
            solution_verified: true,
            verified_by_user_id: resolutionData.resolved_by_user_id,
            verified_at: new Date(),
          },
          {
            where: { id: resolutionData.solution_message_id },
            transaction,
          }
        );
      }

      // Create resolution message
      const resolutionMessage = await db.TicketMessage.create(
        {
          ticket_id: ticketId,
          sender_type: MESSAGE_TYPE.SYSTEM,
          sender_name: "System",
          message_content: `Ticket has been resolved. ${resolutionData.resolution_summary}`,
          message_visibility: "public",
          is_ai_generated: false,
          marked_as_solution: false,
          solution_verified: false,
          organization_id: ticket.organization_id,
          created_by: resolutionData.resolved_by_user_id,
        },
        { transaction }
      );

      return { ticket, resolutionMessage };
    });
  }

  /**
   * Bulk update tickets in a single transaction
   */
  static async bulkUpdateTickets(
    ticketIds: string[],
    updates: any,
    userId: number
  ): Promise<{ updatedCount: number; tickets: any[] }> {
    return TransactionManager.execute(async (transaction) => {
      // Update tickets
      const [updatedCount] = await db.Ticket.update(
        {
          ...updates,
          updated_by: userId,
        },
        {
          where: { id: ticketIds },
          transaction,
        }
      );

      // Get updated tickets
      const tickets = await db.Ticket.findAll({
        where: { id: ticketIds },
        transaction,
      });

      // Create system messages for each ticket
      for (const ticket of tickets) {
        await db.TicketMessage.create(
          {
            ticket_id: ticket.id,
            sender_type: MESSAGE_TYPE.SYSTEM,
            sender_name: "System",
            message_content: `Ticket updated via bulk operation.`,
            message_visibility: "internal",
            is_ai_generated: false,
            marked_as_solution: false,
            solution_verified: false,
            organization_id: ticket.organization_id,
            created_by: userId,
          },
          { transaction }
        );
      }

      return { updatedCount, tickets };
    });
  }
}

/**
 * Utility functions for transaction management
 */
export const withTransaction = async <T>(
  operation: (transaction: SequelizeTransaction) => Promise<T>
): Promise<T> => {
  return TransactionManager.execute(operation);
};

/**
 * Retry transaction on deadlock or timeout
 */
export const retryTransaction = async <T>(
  operation: (transaction: SequelizeTransaction) => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await TransactionManager.execute(operation);
    } catch (error: any) {
      lastError = error;

      // Check if error is retryable (deadlock, timeout, etc.)
      const isRetryable =
        error.name === "SequelizeDeadlockError" ||
        error.name === "SequelizeTimeoutError" ||
        error.name === "SequelizeConnectionError";

      if (!isRetryable || attempt === maxRetries) {
        throw error;
      }

      console.warn(
        `Transaction attempt ${attempt} failed, retrying in ${delay}ms...`,
        error.message
      );
      await new Promise((resolve) => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
};

export default TransactionManager;
