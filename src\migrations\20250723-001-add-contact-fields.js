"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔄 Adding contact information fields to mo_support_tickets...');
    
    // Add contact information fields to mo_support_tickets table
    const contactFields = [
      {
        name: 'ticket_owner_name',
        definition: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Manual override for ticket owner name (if provided by user)'
        }
      },
      {
        name: 'ticket_owner_email',
        definition: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Manual override for ticket owner email (if provided by user)'
        }
      },
      {
        name: 'ticket_owner_phone',
        definition: {
          type: Sequelize.STRING(50),
          allowNull: true,
          comment: 'Manual override for ticket owner phone (if provided by user)'
        }
      }
    ];

    for (const field of contactFields) {
      try {
        await queryInterface.addColumn('mo_support_tickets', field.name, field.definition);
        console.log(`✅ Added column: mo_support_tickets.${field.name}`);
      } catch (error) {
        console.log(`⚠️  Column mo_support_tickets.${field.name} may already exist:`, error.message);
      }
    }

    console.log('🎉 Contact fields migration completed!');
  },

  async down(queryInterface) {
    console.log('🔄 Removing contact information fields from mo_support_tickets...');
    
    const contactFields = ['ticket_owner_name', 'ticket_owner_email', 'ticket_owner_phone'];

    for (const field of contactFields) {
      try {
        await queryInterface.removeColumn('mo_support_tickets', field);
        console.log(`✅ Removed column: mo_support_tickets.${field}`);
      } catch (error) {
        console.log(`⚠️  Error removing mo_support_tickets.${field}:`, error.message);
      }
    }

    console.log('🎉 Contact fields rollback completed!');
  }
};
