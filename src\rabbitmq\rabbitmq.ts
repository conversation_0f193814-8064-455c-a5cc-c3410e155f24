import amqp from "amqplib";
import { RABBITMQ_QUEUE } from "../helper/constant";
import {
  mailSuccessFailedService,
  supportTicketNotificationService,
  supportAnalyticsService,
} from "../services/support.services";

// Get RabbitMQ URL with fallback logic (following TTH pattern)
const getRabbitMQUrl = (): string => {
  return process.env.RABBITMQ_URL || "amqp://localhost:5672";
};

let connection: any = null;

/**
 * Get or create RabbitMQ connection (singleton)
 */
const getConnection = async () => {
  if (!connection) {
    connection = await amqp.connect(getRabbitMQUrl());
    // Handle connection events
    connection.on("error", (err: any) => {
      console.error("RabbitMQ connection error:", err);
      connection = null;
    });
    connection.on("close", () => {
      console.log("RabbitMQ connection closed");
      connection = null;
    });
  }
  return connection;
};

/**
 * Create a new channel for each operation
 */
const createChannel = async () => {
  const conn = await getConnection();
  return await conn.createChannel();
};

/**
 * Publish a message to a RabbitMQ queue
 */
const publishMessage = async (queue: string, message: any) => {
  const channel = await createChannel();
  try {
    await channel.assertQueue(queue, { durable: true });
    channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)), {
      persistent: true,
    });
    console.log(`Message published to queue "${queue}"`);
  } catch (err) {
    console.error("Error publishing message:", err);
    throw err;
  } finally {
    await channel.close();
  }
};

/**
 * Consume messages from a RabbitMQ queue
 * (Handler logic should be implemented in the consumer/worker)
 */
const consumeMessage = async (
  queue: string,
  handler: (msg: any, routingKey: string) => Promise<void>
) => {
  try {
    const channel = await createChannel();
    await channel.assertQueue(queue, { durable: true });
    console.log(`Waiting for messages in queue: ${queue}`);

    channel.consume(queue, async (msg: any) => {
      if (!msg) return;
      try {
        const message = JSON.parse(msg.content.toString());
        const routingKey = msg.fields.routingKey;
        await handler(message, routingKey);
        channel.ack(msg);
      } catch (error) {
        console.error(`Error processing message:`, error);
        channel.nack(msg, false, false); // Don't requeue
      }
    });
  } catch (error) {
    console.error(`Error setting up message consumer:`, error);
    throw error;
  }
};

export default { publishMessage, consumeMessage };
