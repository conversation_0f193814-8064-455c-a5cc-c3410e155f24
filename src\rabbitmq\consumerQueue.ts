import rabbitMQ from "./rabbitmq";
import { RABBITMQ_QUEUE } from "../helper/constant";
import {
  mailSuccessFailedService,
  supportTicketNotificationService,
  supportAnalyticsService,
} from "../services/support.services";

export const setupConsumers = async () => {
  try {
    console.log("Setting up RabbitMQ consumers for support-ticket-ms...");

    // Handler for email delivery status (success/failed)
    const mailStatusHandler = async (msg: any, routingKey: string) => {
      try {
        console.log(`Processing mail status: ${routingKey}`, msg);
        await mailSuccessFailedService(msg, routingKey);
      } catch (error) {
        console.error(`Error processing mail status ${routingKey}:`, error);
      }
    };

    // Handler for support ticket analytics/reporting
    const analyticsHandler = async (msg: any, routingKey: string) => {
      try {
        console.log(`Processing analytics data: ${routingKey}`, msg);
        await supportAnalyticsService(msg, routingKey);
      } catch (error) {
        console.error(`Error processing analytics ${routingKey}:`, error);
      }
    };

    // Consume email delivery status feedback (from notify-ms)
    await rabbitMQ.consumeMessage(
      RABBITMQ_QUEUE.MAIL_FAILED,
      mailStatusHandler
    );
    await rabbitMQ.consumeMessage(
      RABBITMQ_QUEUE.MAIL_SUCCESS,
      mailStatusHandler
    );

    // Consume analytics/reporting data if needed
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.SUPPORT_ANALYTICS, analyticsHandler);

    console.log(
      "RabbitMQ consumers for support-ticket-ms are set up successfully."
    );
  } catch (error) {
    console.error(
      "Error setting up RabbitMQ consumers for support-ticket-ms:",
      error
    );
    // Re-throw the error to be handled by the caller
    throw error;
  }
};
