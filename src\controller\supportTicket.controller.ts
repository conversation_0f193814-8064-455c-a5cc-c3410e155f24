import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import {
  getPagination,
  getAssignableUsers,
  getUser,
  resolveContactInformation,
} from "../utils/common";
import { TICKET_STATUS, MESSAGE_TYPE, ACTION_TYPE } from "../helper/constant";
import {
  getTicketByIdRaw,
  getTicketBySlugRaw,
  getTicketsListRaw,
  generateTicketSlug,
  calculateSlaDueDate,
} from "../helper/ticket.helper";
import {
  isDefaultAccess,
  hasFullTicketAccess,
  validateUserExists,
  isAgent,
} from "../utils/common";
import { organizationHelper } from "../helper/organization.helper";
import { recordTicketHistory } from "../helper/history.helper";
import {
  sendTicketCreatedNotification,
  sendTicketStatusUpdatedNotification,
  sendTicketAssignedNotification,
  sendTicketResolvedNotification,
} from "../services/notification.service";

const Ticket = db.Ticket;
const TicketMessage = db.TicketMessage;
const TicketAttachment = db.TicketAttachment;
const TicketHistory = db.TicketHistory;

/**
 * Get all support tickets with pagination and filters (following recipe-ms pattern)
 */
const getAllTickets = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page,
      limit,
      search,
      ticket_status,
      ticket_priority,
      ticket_type,
      ticket_module,
      assigned_to_user_id,
      ticket_owner_user_id,
      date_from,
      date_to,
      overdue,
      sort_by,
      sort_order,
      organization_id,
    } = req.query;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Decide organization filter based on query and user role
    let orgFilter: string | undefined = undefined;
    let assignedToFilter: number | undefined = undefined;

    // Check if user is an agent (but not admin) - with error handling
    let isAgentUser = false;
    let isAdminUser = false;

    try {
      isAgentUser = await isAgent(userId);
      isAdminUser = await isDefaultAccess(userId);
    } catch (error) {
      console.error("Error checking user roles:", error);
      // Default to safe behavior - treat as regular user
      isAgentUser = false;
      isAdminUser = false;
    }

    if (organization_id) {
      // Organization filter explicitly provided
      if (hasFullAccess) {
        orgFilter = organization_id as string;
        console.log(
          `User ${userId} with full access - filtering by specified org: ${orgFilter}`
        );
      } else {
        // Regular users can only retrieve their own organization tickets
        orgFilter = organizationId;
        console.log(
          `Regular user ${userId} - filtering by own org: ${orgFilter}`
        );
      }
    } else {
      // No organization filter provided
      if (!hasFullAccess) {
        orgFilter = organizationId; // restrict to own org
        console.log(
          `Regular user ${userId} - filtering by own org: ${orgFilter}`
        );
      } else {
        // For users with full access (admin or agent org), orgFilter remains undefined to fetch all
        console.log(
          `User ${userId} with full access - showing all tickets (including null org)`
        );
      }
    }

    // Agent users (non-admin) should see tickets assigned to them
    // Note: They will also see followed tickets via the follower access logic
    if (isAgentUser && !isAdminUser) {
      assignedToFilter = userId;
    }



    // Use raw query for better performance (following recipe-ms pattern)
    const { limit: queryLimit, offset } = getPagination(
      page as string,
      limit as string
    );

    // Get tickets with proper access control (organization + follower access)
    const { tickets, total } = await getTicketsListRaw(orgFilter, {
      limit: queryLimit,
      offset,
      search: search as string,
      ticket_status: ticket_status as string,
      ticket_priority: ticket_priority as string,
      ticket_type: ticket_type as string,
      ticket_module: ticket_module as string,
      assigned_to_user_id:
        assignedToFilter ||
        (assigned_to_user_id ? Number(assigned_to_user_id) : undefined),
      ticket_owner_user_id: ticket_owner_user_id ? Number(ticket_owner_user_id) : undefined,
      date_from: date_from as string,
      date_to: date_to as string,
      overdue: overdue === "true",
      sort_by: sort_by as string,
      sort_order: sort_order as string,
      // Add follower access for all users (including agents), exclude only super admins
      followerUserId: isAdminUser ? undefined : userId,
    });

    if (!tickets || tickets.length === 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("NO_TICKETS_FOUND") || "No tickets found",
        data: [],
        ...(limit && {
          pagination: {
            total,
            page: Number(page) || 1,
            limit: Number(limit),
            totalPages: Math.ceil(total / Number(limit)),
          },
        }),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKETS_RETRIEVED_SUCCESSFULLY") ||
        "Tickets retrieved successfully",
      data: tickets,
      ...(limit && {
        pagination: {
          total,
          page: Number(page) || 1,
          limit: Number(limit),
          totalPages: Math.ceil(total / Number(limit)),
        },
      }),
    });
  } catch (error: unknown) {
    console.error("Error in getAllTickets:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get single ticket by ID or slug (following recipe-ms pattern)
 */
const getTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    let ticketData;
    const isNumeric = !isNaN(Number(id));

    // For users with full access (admin or agent org), don't filter by organization
    const orgFilter = hasFullAccess ? null : organizationId;

    if (isNumeric) {
      ticketData = await getTicketByIdRaw(Number(id), orgFilter);
    } else {
      ticketData = await getTicketBySlugRaw(id, orgFilter);
    }

    if (!ticketData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Check if current user is agent or admin
    const isAgentUser = await isAgent(userId);
    const isAdminUser = await isDefaultAccess(userId);
    const isAgentOrAdmin = isAgentUser || isAdminUser;

    // Check if current user is a follower of this ticket
    let isFollower = false;
    try {
      if (ticketData?.followers && Array.isArray(ticketData.followers)) {
        // Check if followers is an array of user objects (processed) or user IDs (raw)
        if (ticketData.followers.length > 0) {
          const firstFollower = ticketData.followers[0];
          if (typeof firstFollower === 'object' && firstFollower.id !== undefined) {
            // Array of user objects: [{id: 14, full_name: "...", email: "..."}, ...]
            isFollower = ticketData.followers.some((follower: any) => follower.id === userId);
          } else {
            // Array of user IDs: [14, 15, 16]
            isFollower = ticketData.followers.includes(userId);
          }
        }
      } else if (typeof ticketData?.followers === 'string') {
        // JSON string format
        const followers = JSON.parse(ticketData.followers);
        isFollower = Array.isArray(followers) && followers.includes(userId);
      }
    } catch (error) {
      console.error("Error checking follower status:", error);
      isFollower = false;
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RETRIEVED_SUCCESSFULLY") ||
        "Ticket retrieved successfully",
      data: {
        ...ticketData,
        isAgentOrAdmin,
        isFollower,
      },
    });
  } catch (error: unknown) {
    console.error("Error in getTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Create new support ticket (following recipe-ms pattern)
 */
const createTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      support_pin,
      ticket_owner_name,
      ticket_owner_email,
      ticket_owner_phone,
    } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    // Check user roles for PIN validation
    let isAgentUser = false;
    let isSuperAdminUser = false;

    try {
      isAgentUser = await isAgent(userId);
      isSuperAdminUser = await isDefaultAccess(userId);
    } catch (error) {
      console.error("Error checking user roles:", error);
    }

    if (!userId || (!organizationId && !isSuperAdminUser)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Handle organization ID for admin users
    let effectiveOrganizationId = organizationId;

    // If super admin user doesn't have organization_id, allow null or use request body
    if (isSuperAdminUser && !organizationId) {
      // Use organization_id from request body if provided, otherwise null
      const requestOrgId = req.body.organization_id;
      effectiveOrganizationId = requestOrgId || null;

      if (effectiveOrganizationId === null) {
        console.log(
          `Admin user ${userId} creating ticket with null organization_id`
        );
      }
    }

    // PIN verification logic - required for regular users, optional for admin/agent
    if (!isSuperAdminUser && !isAgentUser) {
      // Regular users must provide support PIN
      if (!support_pin) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("SUPPORT_PIN_REQUIRED") ||
            "Support PIN is required to create tickets",
        });
      }

      // Validate the provided PIN
      const pinValidation = await organizationHelper.validateSupportPinFromDB(
        effectiveOrganizationId,
        support_pin
      );

      if (!pinValidation.isValid) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("INVALID_SUPPORT_PIN") || "Invalid support PIN",
          error: pinValidation.error,
        });
      }
    } else {
      console.log(
        `PIN validation skipped for ${isSuperAdminUser ? "admin" : "agent"} user ${userId}`
      );
    }

    // Generate unique ticket slug
    const ticketSlug = await generateTicketSlug();

    // Fetch organization config for SLA parameters
    const orgConfig = await db.SupportConfig.findOne({
      where: { organization_id: effectiveOrganizationId },
    });

    // Handle SLA and manual date management
    let slaDueDate = null;
    let manualStartDate = null;
    let manualDueDate = null;

    if (orgConfig && orgConfig.sla_enabled) {
      // SLA is enabled - calculate automatic SLA due date
      slaDueDate = calculateSlaDueDate(new Date(), ticket_priority, orgConfig);
    } else {
      // SLA is disabled - use manual dates if provided
      if (req.body.manual_start_date) {
        manualStartDate = new Date(req.body.manual_start_date);
      }
      if (req.body.manual_due_date) {
        manualDueDate = new Date(req.body.manual_due_date);
      }
    }

    // Resolve contact information (manual override or fetch from nv_users)
    const contactInfo = await resolveContactInformation(
      userId,
      ticket_owner_name,
      ticket_owner_email,
      ticket_owner_phone
    );

    const ticketData = {
      ticket_slug: ticketSlug,
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      ticket_status: TICKET_STATUS.OPEN,
      organization_id: effectiveOrganizationId,
      ticket_owner_user_id: userId,
      created_by_user_id: userId,
      updated_by_user_id: userId,
      sla_due_date: slaDueDate,
      manual_start_date: manualStartDate,
      manual_due_date: manualDueDate,
      // Contact information (manual override or from nv_users)
      ticket_owner_name:
        contactInfo.ticket_owner_name || contactInfo.resolved_name,
      ticket_owner_email:
        contactInfo.ticket_owner_email || contactInfo.resolved_email,
      ticket_owner_phone:
        contactInfo.ticket_owner_phone || contactInfo.resolved_phone,
      // Initialize time tracking fields
      work_started_at: null,
      work_completed_at: null,
      estimated_hours: null,
      actual_hours: null,
      time_logged_by_user_id: null,
    };

    let ticket;
    try {
      ticket = await Ticket.create(ticketData);
    } catch (error: any) {
      // Handle duplicate slug error by regenerating
      if (
        error.name === "SequelizeUniqueConstraintError" &&
        error.fields?.ticket_slug
      ) {
        console.log("Duplicate slug detected, regenerating...");
        const newSlug = await generateTicketSlug();
        ticketData.ticket_slug = newSlug;
        ticket = await Ticket.create(ticketData);
      } else {
        throw error;
      }
    }

    // Record ticket creation in history
    await db.TicketHistory.create({
      ticket_id: ticket.id,
      action_type: ACTION_TYPE.CREATED,
      previous_status: null,
      new_status: TICKET_STATUS.OPEN,
      field_changed: "ticket_status",
      old_value: "",
      new_value: TICKET_STATUS.OPEN,
      change_note: `Ticket created: ${ticket_title}`,
      created_by: userId,
    });

    // Handle file uploads exactly like recipe microservice
    const uploadedAttachments: any[] = [];
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle ticket files upload
      if (files.ticketFiles && files.ticketFiles.length > 0) {
        const { TicketAttachment } = db;

        for (const file of files.ticketFiles) {
          // Determine attachment type based on MIME type
          let attachmentType = "document";
          if (file.mimetype.startsWith("image/")) {
            attachmentType = "image";
          } else if (file.mimetype.startsWith("video/")) {
            attachmentType = "video";
          } else if (file.mimetype.startsWith("audio/")) {
            attachmentType = "audio";
          }

          // Create attachment record matching the model structure
          const attachmentData = {
            ticket_id: ticket.id,
            item_id: file.item_id,
            attachment_type: attachmentType,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            created_by: userId,
          };

          const attachment = await TicketAttachment.create(attachmentData);
          uploadedAttachments.push({
            id: attachment.id,
            item_id: file.item_id,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            attachment_type: attachmentType,
          });
        }
      }
    }

    // Include attachments in response if any were uploaded
    const responseData = {
      ...ticket.toJSON(),
      attachments:
        uploadedAttachments.length > 0 ? uploadedAttachments : undefined,
    };

    // Send ticket created notification
    try {
      // Get user details for notification
      const userDetails = await getUser(userId);
      if (userDetails && userDetails.user_email) {
        await sendTicketCreatedNotification({
          ticket_number: ticketSlug,
          submitter_email: userDetails.user_email,
          submitter_name:
            `${userDetails.user_first_name || ""} ${userDetails.user_last_name || ""}`.trim() ||
            userDetails.user_email,
          subject: ticket_title,
          organization_id: effectiveOrganizationId,
          user_id: userId,
          from_user_id: userId,
        });
      }
    } catch (notificationError) {
      console.error(
        "Failed to send ticket created notification:",
        notificationError
      );
      // Don't fail the ticket creation if notification fails
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message:
        res.__("TICKET_CREATED_SUCCESSFULLY") || "Ticket created successfully",
      data: responseData,
    });
  } catch (error: unknown) {
    console.error("Error in createTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Update support ticket (following recipe-ms pattern)
 */
const updateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Check user roles for PIN validation
    let isAgentUser = false;
    let isAdminUser = false;

    try {
      isAgentUser = await isAgent(userId);
      isAdminUser = await isDefaultAccess(userId);
    } catch (error) {
      console.error("Error checking user roles for PIN validation:", error);
    }

    // PIN verification logic - required for regular users, optional for admin/agent
    if (!isAdminUser && !isAgentUser) {
      // Regular users must provide support PIN
      if (!updateData.support_pin) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("SUPPORT_PIN_REQUIRED") ||
            "Support PIN is required for ticket updates",
        });
      }

      // Validate the provided PIN
      const pinValidation = await organizationHelper.validateSupportPinFromDB(
        organizationId,
        updateData.support_pin
      );

      if (!pinValidation.isValid) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("PIN_VERIFICATION_FAILED") || "Invalid support PIN provided",
          error: pinValidation.error,
        });
      }
    } else {
      console.log(
        `PIN validation skipped for ${isAdminUser ? "admin" : "agent"} user ${userId}`
      );
    }

    // Remove PIN from update data as it's not a ticket field
    if (updateData.support_pin) {
      delete updateData.support_pin;
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Add audit fields
    updateData.updated_by_user_id = userId;

    // Check if assignment change is requested
    const assignmentChanged =
      updateData.assigned_to_user_id &&
      updateData.assigned_to_user_id !== ticket.assigned_to_user_id;

    if (assignmentChanged) {
      // Validate assignee exists and is active
      const assigneeId = Number(updateData.assigned_to_user_id);
      if (!assigneeId || !(await validateUserExists(assigneeId))) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("INVALID_ASSIGNEE") ||
            "Assignee user does not exist or is inactive",
        });
      }

      // Optional: Ensure the assignee belongs to same organization unless current user is default access
      if (!(await isDefaultAccess(userId))) {
        const assignee = await getUser(assigneeId);
        if (!assignee || assignee.organization_id !== organizationId) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message:
              res.__("ASSIGNEE_OUTSIDE_ORG") ||
              "Cannot assign ticket to a user outside your organization",
          });
        }
      }

      updateData.assigned_at = new Date();
      updateData.assigned_by_user_id = userId;
      // If status not explicitly set, default to ASSIGNED
      if (!updateData.ticket_status) {
        updateData.ticket_status = TICKET_STATUS.ASSIGNED;
      }
    }

    // Validate time tracking inputs
    if (updateData.estimated_hours !== undefined) {
      if (updateData.estimated_hours < 0 || updateData.estimated_hours > 1000) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("INVALID_ESTIMATED_HOURS") ||
            "Estimated hours must be between 0 and 1000",
        });
      }
    }

    if (updateData.actual_hours !== undefined) {
      if (updateData.actual_hours < 0 || updateData.actual_hours > 1000) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("INVALID_ACTUAL_HOURS") ||
            "Actual hours must be between 0 and 1000",
        });
      }
    }

    // Get organization config for SLA and validation logic
    const orgConfig = await db.SupportConfig.findOne({
      where: { organization_id: organizationId },
    });

    // Validate status change to IN_PROGRESS - must be assigned to a user
    if (
      updateData.ticket_status === TICKET_STATUS.IN_PROGRESS &&
      updateData.ticket_status !== ticket.ticket_status
    ) {
      // Check if ticket is assigned (either already assigned or being assigned in this update)
      const assignedUserId =
        updateData.assigned_to_user_id || ticket.assigned_to_user_id;

      if (!assignedUserId) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: orgConfig?.sla_enabled
            ? res.__("TICKET_MUST_BE_ASSIGNED_FOR_PROGRESS") ||
              "Ticket must be assigned to a user before it can be set to in progress"
            : res.__("ASSIGN_USER_TO_START_WORK") ||
              "Please assign the ticket to a user to start work",
        });
      }
    }

    // Handle manual date management when SLA is disabled
    if (orgConfig && !orgConfig.sla_enabled) {
      // Allow manual start and due date updates without requiring reasons
      if (req.body.manual_start_date) {
        updateData.manual_start_date = new Date(req.body.manual_start_date);
      }
      if (req.body.manual_due_date) {
        updateData.manual_due_date = new Date(req.body.manual_due_date);
      }
      // Clear any reason fields since they're not required for manual mode
      if (req.body.manual_start_date || req.body.manual_due_date) {
        updateData.date_override_reason = null;
        updateData.date_override_by_user_id = userId;
      }
    }

    // Capture old values for history
    const oldData = ticket.toJSON();

    // Automatic time tracking based on status changes
    if (
      updateData.ticket_status &&
      updateData.ticket_status !== ticket.ticket_status
    ) {
      const currentTime = new Date();

      // Start work tracking when status changes to "in_progress"
      if (
        updateData.ticket_status === TICKET_STATUS.IN_PROGRESS &&
        !ticket.work_started_at
      ) {
        updateData.work_started_at = currentTime;
        updateData.time_logged_by_user_id = userId;
        console.log(
          `Work started automatically for ticket ${ticket.id} by user ${userId}`
        );
      }

      // Complete work tracking when status changes to "resolved" or "closed"
      if (
        (updateData.ticket_status === TICKET_STATUS.RESOLVED ||
          updateData.ticket_status === TICKET_STATUS.CLOSED) &&
        ticket.work_started_at &&
        !ticket.work_completed_at
      ) {
        updateData.work_completed_at = currentTime;
        updateData.time_logged_by_user_id = userId;

        // Calculate actual hours if work was started
        if (ticket.work_started_at) {
          const timeDiff =
            currentTime.getTime() - ticket.work_started_at.getTime();
          const hoursWorked =
            Math.round((timeDiff / (1000 * 60 * 60)) * 100) / 100; // Round to 2 decimal places

          // Ensure reasonable hours (max 1000 hours, min 0.01 hours)
          const validHours = Math.max(0.01, Math.min(1000, hoursWorked));
          updateData.actual_hours = validHours;

          console.log(
            `Work completed for ticket ${ticket.id}. Total hours: ${validHours}`
          );
        }
      }

      // Reset work tracking if status goes back to earlier stages
      if (
        (ticket.ticket_status === TICKET_STATUS.RESOLVED ||
          ticket.ticket_status === TICKET_STATUS.CLOSED) &&
        (updateData.ticket_status === TICKET_STATUS.IN_PROGRESS ||
          updateData.ticket_status === TICKET_STATUS.ASSIGNED ||
          updateData.ticket_status === TICKET_STATUS.OPEN)
      ) {
        updateData.work_completed_at = null;
        // Keep work_started_at and actual_hours for reference
      }
    }

    // Handle SLA recalculation based on priority changes
    if (
      updateData.ticket_priority &&
      updateData.ticket_priority !== ticket.ticket_priority
    ) {
      if (orgConfig && orgConfig.sla_enabled && !ticket.manual_due_date) {
        // SLA is enabled and no manual override - recalculate SLA
        const newSlaDueDate = calculateSlaDueDate(
          new Date(),
          updateData.ticket_priority,
          orgConfig
        );
        if (newSlaDueDate) {
          updateData.sla_due_date = newSlaDueDate;
        }
      } else if (orgConfig && !orgConfig.sla_enabled) {
        // SLA is disabled - clear automatic SLA date, keep manual dates
        updateData.sla_due_date = null;
      }
    }

    // Handle manual date overrides (agent/admin only)
    if (updateData.manual_start_date || updateData.manual_due_date) {
      if (!hasFullAccess && !(await isAgent(userId))) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message:
            res.__("MANUAL_DATES_PERMISSION_DENIED") ||
            "Only agents can set manual dates",
        });
      }

      // Set override metadata
      if (updateData.manual_start_date || updateData.manual_due_date) {
        updateData.date_override_by_user_id = userId;
        if (!updateData.date_override_reason) {
          updateData.date_override_reason = "Manual date override by agent";
        }
      }
    }

    // Handle followers (validate user IDs exist and organization access)
    if (updateData.followers && Array.isArray(updateData.followers)) {
      for (const followerId of updateData.followers) {
        const followerExists = await validateUserExists(followerId);
        if (!followerExists) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message:
              res.__("FOLLOWER_NOT_FOUND") ||
              `User ID ${followerId} does not exist`,
          });
        }

        // Ensure the follower belongs to same organization unless current user is default access
        if (!(await isDefaultAccess(userId))) {
          const follower = await getUser(followerId);
          if (!follower || follower.organization_id !== organizationId) {
            return res.status(StatusCodes.BAD_REQUEST).json({
              status: false,
              message:
                res.__("FOLLOWER_OUTSIDE_ORG") ||
                "Cannot add a user from outside your organization as follower",
            });
          }
        }
      }
    }

    // Handle SLA pause (agent/admin only)
    if (updateData.sla_paused !== undefined) {
      if (!hasFullAccess && !(await isAgent(userId))) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message:
            res.__("SLA_PAUSE_PERMISSION_DENIED") ||
            "Only agents can pause SLA",
        });
      }

      if (updateData.sla_paused && !updateData.sla_pause_reason) {
        updateData.sla_pause_reason = "SLA paused by agent";
      }
    }

    await ticket.update(updateData);

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    // Send notifications for status changes and assignments
    try {
      // Get ticket owner details for notifications
      const ticketOwnerDetails = await getUser(ticket.ticket_owner_user_id);

      if (ticketOwnerDetails && ticketOwnerDetails.user_email) {
        // Send status update notification if status changed
        if (
          updateData.ticket_status &&
          updateData.ticket_status !== oldData.ticket_status
        ) {
          await sendTicketStatusUpdatedNotification({
            ticket_number: ticket.ticket_slug,
            submitter_email: ticketOwnerDetails.user_email,
            submitter_name:
              `${ticketOwnerDetails.user_first_name || ""} ${ticketOwnerDetails.user_last_name || ""}`.trim() ||
              ticketOwnerDetails.user_email,
            subject: ticket.ticket_title,
            previous_status: oldData.ticket_status,
            new_status: updateData.ticket_status,
            resolution_note: updateData.resolution_note || "",
            organization_id: ticket.organization_id,
            user_id: ticket.ticket_owner_user_id,
            from_user_id: userId,
          });
        }

        // Send assignment notification if ticket was assigned
        if (assignmentChanged) {
          const assigneeDetails = await getUser(updateData.assigned_to_user_id);
          await sendTicketAssignedNotification({
            ticket_number: ticket.ticket_slug,
            submitter_email: ticketOwnerDetails.user_email,
            submitter_name:
              `${ticketOwnerDetails.user_first_name || ""} ${ticketOwnerDetails.user_last_name || ""}`.trim() ||
              ticketOwnerDetails.user_email,
            subject: ticket.ticket_title,
            assigned_to: assigneeDetails
              ? `${assigneeDetails.user_first_name || ""} ${assigneeDetails.user_last_name || ""}`.trim() ||
                assigneeDetails.user_email
              : "Support Team",
            organization_id: ticket.organization_id,
            user_id: ticket.ticket_owner_user_id,
            from_user_id: userId,
          });
        }
      }
    } catch (notificationError) {
      console.error(
        "Failed to send ticket update notifications:",
        notificationError
      );
      // Don't fail the update if notification fails
    }

    const uploadedAttachments: any[] = [];
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle ticket files upload
      if (files.ticketFiles && files.ticketFiles.length > 0) {
        const { TicketAttachment } = db;

        for (const file of files.ticketFiles) {
          // Determine attachment type based on MIME type
          let attachmentType = "document";
          if (file.mimetype.startsWith("image/")) {
            attachmentType = "image";
          } else if (file.mimetype.startsWith("video/")) {
            attachmentType = "video";
          } else if (file.mimetype.startsWith("audio/")) {
            attachmentType = "audio";
          }

          // Create attachment record matching the model structure
          const attachmentData = {
            ticket_id: ticket.id,
            item_id: file.item_id,
            attachment_type: attachmentType,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            created_by: userId,
          };

          const attachment = await TicketAttachment.create(attachmentData);
          uploadedAttachments.push({
            id: attachment.id,
            item_id: file.item_id,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            attachment_type: attachmentType,
          });
        }
      }
    }

    const responseData = {
      ...ticket.toJSON(),
      attachments:
        uploadedAttachments.length > 0 ? uploadedAttachments : undefined,
    };

    // Determine user-friendly message based on what was updated using i18n
    let messageKey = "USER_FRIENDLY_UPDATE_SUCCESS";
    if (assignmentChanged) {
      messageKey = "TICKET_ASSIGNED_TO_TEAM";
    } else if (updateData.followers) {
      messageKey = "FOLLOWERS_ADDED_SUCCESSFULLY";
    } else if (updateData.manual_start_date || updateData.manual_due_date) {
      messageKey = "MANUAL_DATES_UPDATED";
    } else if (updateData.sla_paused) {
      messageKey = "SLA_PAUSED_SUCCESSFULLY";
    } else if (
      updateData.ticket_priority &&
      updateData.ticket_priority !== oldData.ticket_priority
    ) {
      messageKey = "TICKET_PRIORITY_CHANGED";
    } else if (
      updateData.ticket_status &&
      updateData.ticket_status !== oldData.ticket_status
    ) {
      messageKey = "TICKET_STATUS_CHANGED";
    }

    const userMessage =
      res.__(messageKey) || "Your support ticket has been updated successfully";

    // Add time tracking summary to response
    const timeTrackingSummary = {
      estimated_hours: responseData.estimated_hours,
      actual_hours: responseData.actual_hours,
      work_started_at: responseData.work_started_at,
      work_completed_at: responseData.work_completed_at,
      is_work_in_progress:
        responseData.work_started_at && !responseData.work_completed_at,
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: userMessage,
      data: {
        ...responseData,
        time_tracking: timeTrackingSummary,
      },
    });
  } catch (error: any) {
    console.error("Error in updateTicket:", error);
    // Handle foreign key errors gracefully
    if (error?.name === "SequelizeForeignKeyConstraintError") {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          res.__("INVALID_ASSIGNEE") ||
          "Assignee user does not exist or is inactive",
      });
    }
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Delete support ticket (following recipe-ms pattern)
 */
const deleteTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Hard delete: remove ticket and its related data permanently
    // Delete child records first to maintain referential integrity
    await TicketMessage.destroy({
      where: { ticket_id: ticket.id },
      force: true,
    });
    await TicketAttachment.destroy({
      where: { ticket_id: ticket.id },
      force: true,
    });
    await TicketHistory.destroy({
      where: { ticket_id: ticket.id },
      force: true,
    });

    // Finally, delete the ticket itself
    await ticket.destroy({ force: true });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_DELETED_SUCCESSFULLY") || "Ticket deleted successfully",
    });
  } catch (error: unknown) {
    console.error("Error in deleteTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Assign ticket to user (following recipe-ms pattern)
 */
const assignTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { assigned_to_user_id } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Get organization config to determine SLA mode
    const orgConfig = await db.SupportConfig.findOne({
      where: { organization_id: ticket.organization_id },
    });

    const oldData = ticket.toJSON();
    const currentTime = new Date();

    // Prepare update data
    const updateData: any = {
      assigned_to_user_id: assigned_to_user_id,
      assigned_at: currentTime,
      assigned_by_user_id: userId,
      ticket_status: TICKET_STATUS.ASSIGNED,
      updated_by_user_id: userId,
    };

    // If auto-start is requested or if it's a manual SLA mode, optionally start work immediately
    if (req.body.auto_start_work === true) {
      updateData.ticket_status = TICKET_STATUS.IN_PROGRESS;
      updateData.work_started_at = currentTime;
      updateData.time_logged_by_user_id = userId;
    }

    await ticket.update(updateData);

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    // Send ticket assignment notification
    try {
      // Get ticket owner details for notification
      const ticketOwnerDetails = await getUser(ticket.ticket_owner_user_id);
      if (ticketOwnerDetails && ticketOwnerDetails.user_email) {
        const assigneeDetails = await getUser(assigned_to_user_id);
        await sendTicketAssignedNotification({
          ticket_number: ticket.ticket_slug,
          submitter_email: ticketOwnerDetails.user_email,
          submitter_name:
            `${ticketOwnerDetails.user_first_name || ""} ${ticketOwnerDetails.user_last_name || ""}`.trim() ||
            ticketOwnerDetails.user_email,
          subject: ticket.ticket_title,
          assigned_to: assigneeDetails
            ? `${assigneeDetails.user_first_name || ""} ${assigneeDetails.user_last_name || ""}`.trim() ||
              assigneeDetails.user_email
            : "Support Team",
          organization_id: ticket.organization_id,
          user_id: ticket.ticket_owner_user_id,
          from_user_id: userId,
        });
      }
    } catch (notificationError) {
      console.error(
        "Failed to send ticket assignment notification:",
        notificationError
      );
      // Don't fail the assignment if notification fails
    }

    // Re-fetch with joins for full info
    const orgFilter = hasFullAccess ? null : organizationId;
    const enrichedTicket = await getTicketByIdRaw(ticket.id, orgFilter);

    // Customize success message based on SLA mode
    const successMessage = orgConfig?.sla_enabled
      ? res.__("TICKET_ASSIGNED_SUCCESSFULLY") || "Ticket assigned successfully"
      : res.__("TICKET_ASSIGNED_READY_TO_START") ||
        "Ticket assigned successfully. You can now start work on this ticket.";

    return res.status(StatusCodes.OK).json({
      status: true,
      message: successMessage,
      data: enrichedTicket,
    });
  } catch (error: unknown) {
    console.error("Error in assignTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Resolve ticket (following recipe-ms pattern)
 */
const resolveTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { resolution_note } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const oldData = ticket.toJSON();

    await ticket.update({
      ticket_status: TICKET_STATUS.RESOLVED,
      resolution_note,
      resolved_at: new Date(),
      resolved_by_user_id: userId,
      updated_by_user_id: userId,
    });

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    // Send ticket resolved notification
    try {
      // Get ticket owner details for notification
      const ticketOwnerDetails = await getUser(ticket.ticket_owner_user_id);
      if (ticketOwnerDetails && ticketOwnerDetails.user_email) {
        await sendTicketResolvedNotification({
          ticket_number: ticket.ticket_slug,
          submitter_email: ticketOwnerDetails.user_email,
          submitter_name:
            `${ticketOwnerDetails.user_first_name || ""} ${ticketOwnerDetails.user_last_name || ""}`.trim() ||
            ticketOwnerDetails.user_email,
          subject: ticket.ticket_title,
          resolution_note: resolution_note || "",
          organization_id: ticket.organization_id,
          user_id: ticket.ticket_owner_user_id,
          from_user_id: userId,
        });
      }
    } catch (notificationError) {
      console.error(
        "Failed to send ticket resolved notification:",
        notificationError
      );
      // Don't fail the resolution if notification fails
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RESOLVED_SUCCESSFULLY") ||
        "Ticket resolved successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in resolveTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Rate ticket (following recipe-ms pattern)
 */
const rateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { rating, review_comment } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const oldData = ticket.toJSON();

    await ticket.update({
      rating,
      review_comment,
      rated_at: new Date(),
      rated_by_user_id: userId,
      updated_by_user_id: userId,
    });

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RATED_SUCCESSFULLY") || "Ticket rated successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in rateTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Add comment/note to ticket (for additional information)
 */
const addTicketComment = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { comment_text, is_private = false } = req.body;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Verify ticket exists and user has access
    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Handle file upload from S3 middleware (same as messages)
    let attachmentItemId = null;
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      if (files.attachment && files.attachment.length > 0) {
        const uploadedFile = files.attachment[0];
        attachmentItemId = uploadedFile.item_id;
      }
    }

    // Create ticket comment
    const comment = await TicketMessage.create({
      ticket_id: Number(id),
      message_text: comment_text,
      message_type: MESSAGE_TYPE.TICKET_COMMENT,
      is_private,
      attachment_id: attachmentItemId,
      created_by: userId,
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message:
        res.__("TICKET_COMMENT_ADDED") ||
        "Comment added to ticket successfully",
      data: comment,
    });
  } catch (error: unknown) {
    console.error("Error in addTicketComment:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get comments for a ticket (additional information)
 */
const getTicketComments = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { page, limit } = req.query;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Verify ticket exists and user has access
    let whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // For comments/conversation API: Only paginate if limit is explicitly provided
    let pagination: any = { limit: undefined, offset: undefined };
    let isPaginated = false;

    if (limit) {
      // Only apply pagination if limit is explicitly provided
      pagination = getPagination(page as string, limit as string);
      isPaginated = true;
    }

    whereClause = {
      ticket_id: Number(id),
      message_type: MESSAGE_TYPE.TICKET_COMMENT,
    };

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      order: [["created_at", "DESC"]], // Latest messages first
    };

    // Only add pagination if parameters provided
    if (isPaginated && pagination.limit) {
      queryOptions.limit = pagination.limit;
      queryOptions.offset = pagination.offset;
    }

    const { rows: comments, count } =
      await TicketMessage.findAndCountAll(queryOptions);

    // Format response
    let responseData: any;
    if (isPaginated) {
      const totalPages = Math.ceil(count / pagination.limit!);
      responseData = {
        comments,
        pagination: {
          currentPage: parseInt(page as string) || 1,
          totalPages,
          totalItems: count,
          itemsPerPage: pagination.limit,
        },
      };
    } else {
      responseData = {
        comments,
        totalCount: count,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_COMMENTS_RETRIEVED") ||
        "Ticket comments retrieved successfully",
      data: responseData,
    });
  } catch (error: unknown) {
    console.error("Error in getTicketComments:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get history records for a ticket
 */
const getTicketHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // ensure ticket belongs to same organization (unless admin or agent)
    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const history = await db.TicketHistory.findAll({
      where: { ticket_id: ticket.id },
      order: [["created_at", "DESC"]], // Latest first
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("HISTORY_FETCHED_SUCCESSFULLY") ||
        "Ticket history retrieved successfully",
      data: history,
    });
  } catch (error) {
    console.error("Error in getTicketHistory:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message:
        res.__("HISTORY_FETCH_FAILED") || "Failed to retrieve ticket history",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get assignable users for ticket assignment
 */
const getAssignableUsersForTicket = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const organizationId = (req as any).user?.organization_id;
    const {
      search,
      roleFilter,
      departmentId,
      branchId,
      page,
      limit,
      includeInactive,
    } = req.query;

    const userId = (req as any).user?.id;
    const isSuperAdminUser = await isDefaultAccess(userId);

    if (!userId || (!organizationId && !isSuperAdminUser)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const options = {
      search: search as string,
      roleFilter: roleFilter as string,
      departmentId: departmentId ? Number(departmentId) : undefined,
      branchId: branchId ? Number(branchId) : undefined,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      includeInactive: includeInactive === "true",
    };

    const result = await getAssignableUsers(organizationId, options);

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("USERS_RETRIEVED_SUCCESSFULLY") ||
        "Users retrieved successfully",
      data: result,
    });
  } catch (error: unknown) {
    console.error("Error in getAssignableUsersForTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Export all controller functions (following recipe-ms pattern)
export default {
  getAllTickets,
  getTicket,
  createTicket,
  updateTicket,
  deleteTicket,
  assignTicket,
  resolveTicket,
  rateTicket,
  addTicketComment,
  getTicketComments,
  getTicketHistory,
  getAssignableUsersForTicket,
};
