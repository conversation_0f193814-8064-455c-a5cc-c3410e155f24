<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Resolved - {{ticket_number}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.6;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .logo-container {
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 120px;
            max-height: 60px;
            border-radius: 4px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #333333;
        }
        .success-banner {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .success-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .success-title {
            font-size: 20px;
            font-weight: 600;
            color: #155724;
            margin-bottom: 10px;
        }
        .success-message {
            color: #155724;
            font-size: 16px;
        }
        .resolution-info {
            background-color: #f8f9fa;
            border-left: 4px solid #4CAF50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .resolution-info h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            color: #333333;
            flex: 1;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            background-color: #28a745;
            color: white;
        }
        .resolution-details {
            background-color: #e8f5e8;
            border: 1px solid #c3e6cb;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .resolution-details h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }
        .resolution-text {
            color: #155724;
            line-height: 1.6;
            font-size: 15px;
        }
        .feedback-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }
        .feedback-section h4 {
            margin: 0 0 15px 0;
            color: #856404;
        }
        .rating-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        .rating-button {
            padding: 10px 20px;
            border: 2px solid #ffc107;
            background-color: #fff;
            color: #856404;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .rating-button:hover {
            background-color: #ffc107;
            color: #fff;
        }
        .next-steps {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .next-steps h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #4CAF50;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            .rating-buttons {
                flex-direction: column;
                align-items: center;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            {{#if ORGANIZATION_LOGO}}
            <div class="logo-container">
                <img src="{{ORGANIZATION_LOGO}}" alt="{{ORGANIZATION_NAME}}" />
            </div>
            {{/if}}
            <h1>✅ Ticket Resolved</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello <strong>{{submitter_name}}</strong>,
            </div>

            <div class="success-banner">
                <div class="success-icon">🎉</div>
                <div class="success-title">Issue Successfully Resolved!</div>
                <div class="success-message">Your support ticket has been completed and marked as resolved.</div>
            </div>

            <div class="resolution-info">
                <h3>📋 Resolution Summary</h3>
                <div class="info-row">
                    <div class="info-label">Ticket Number:</div>
                    <div class="info-value"><strong>{{ticket_number}}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Subject:</div>
                    <div class="info-value">{{subject}}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Status:</div>
                    <div class="info-value">
                        <span class="status-badge">Resolved</span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Resolved Date:</div>
                    <div class="info-value">{{CURRENT_DATE}}</div>
                </div>
            </div>

            {{#if resolution_note}}
            <div class="resolution-details">
                <h4>🔧 Resolution Details</h4>
                <div class="resolution-text">{{resolution_note}}</div>
            </div>
            {{/if}}

            <div class="next-steps">
                <h4>✨ What's next?</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Test the solution:</strong> Please verify that the issue has been resolved</li>
                    <li><strong>No action needed:</strong> If everything works correctly, your ticket is complete</li>
                    <li><strong>Need more help?</strong> Simply reply to this email if you need additional assistance</li>
                    <li><strong>Reopen if needed:</strong> We can reopen the ticket if the issue persists</li>
                </ul>
            </div>

            <div class="feedback-section">
                <h4>📝 How did we do?</h4>
                <p>Your feedback helps us improve our support service. Please take a moment to rate your experience:</p>
                <div class="rating-buttons">
                    <a href="#" class="rating-button">😊 Excellent</a>
                    <a href="#" class="rating-button">👍 Good</a>
                    <a href="#" class="rating-button">👎 Needs Improvement</a>
                </div>
                <p style="font-size: 14px; color: #6c757d; margin-top: 15px;">
                    You can also reply to this email with any additional feedback or suggestions.
                </p>
            </div>

            <p style="margin-top: 30px; text-align: center; font-size: 16px;">
                <strong>Thank you for choosing {{ORGANIZATION_NAME}} support!</strong><br>
                We're here whenever you need assistance.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ORGANIZATION_NAME}} Support Team</strong></p>
            <p>This is an automated message. Please do not reply directly to this email.</p>
            <p>© {{CURRENT_YEAR}} {{ORGANIZATION_NAME}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
