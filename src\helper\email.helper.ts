import fs from "fs";
import path from "path";
import handlebars, { TemplateDelegate } from "handlebars";

export class EmailTemplateHelper {
  private static templateCache: { [key: string]: TemplateDelegate } = {};

  static async getCompiledTemplate(
    templateName: string
  ): Promise<TemplateDelegate> {
    if (this.templateCache[templateName]) {
      return this.templateCache[templateName];
    }

    const templatePath = path.join(
      __dirname,
      "..",
      "templates",
      "email",
      `${templateName}.hbs`
    );
    const templateContent = await fs.promises.readFile(templatePath, "utf-8");
    const compiledTemplate = handlebars.compile(templateContent);

    this.templateCache[templateName] = compiledTemplate;
    return compiledTemplate;
  }

  static async renderTemplate(
    templateName: string,
    data: Record<string, unknown>
  ): Promise<string> {
    const template = await this.getCompiledTemplate(templateName);
    return template(data);
  }
}
