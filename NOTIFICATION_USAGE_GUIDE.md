# Support Ticket Notification System - Usage Guide

## Quick Start

The notification system is now fully integrated into the support ticket microservice. Here's what happens automatically:

### 🎯 **Automatic Notifications**

#### 1. **Ticket Creation**
When a user creates a ticket via `POST /api/v1/tickets`:
- ✅ **Email sent** to ticket submitter confirming creation
- ✅ **In-app notification** created for the user
- ✅ **Push notification** queued (if mobile app configured)

#### 2. **Status Changes**
When ticket status changes via `PUT /api/v1/tickets/:id`:
- ✅ **Email sent** with status update details
- ✅ **In-app notification** showing status transition
- ✅ **Push notification** for real-time updates

#### 3. **Ticket Assignment**
When ticket is assigned via `POST /api/v1/tickets/:id/assign`:
- ✅ **Email sent** to ticket owner with assignee details
- ✅ **In-app notification** with agent information
- ✅ **Push notification** for immediate awareness

#### 4. **Ticket Resolution**
When ticket is resolved via `POST /api/v1/tickets/:id/resolve`:
- ✅ **Email sent** with resolution details
- ✅ **In-app notification** with resolution notes
- ✅ **Push notification** for closure confirmation

## 🔧 **Configuration**

### Environment Variables
```bash
# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# Organization Configuration
ORGANIZATION_LOGO_URL=https://yourcompany.com/logo.png
```

### Database Setup
Ensure the following tables exist:
- `users` - For user information and email addresses
- `nv_notification_meta` - For in-app notifications (optional)

## 📧 **Email Templates**

Templates are located in `src/templates/email/`:

### Available Templates:
1. **`ticket_created.hbs`** - New ticket confirmation
2. **`ticket_status_updated.hbs`** - Status change notifications
3. **`ticket_assigned.hbs`** - Assignment notifications
4. **`ticket_resolved.hbs`** - Resolution notifications

### Customizing Templates:
```html
<!-- Example: ticket_created.hbs -->
<!DOCTYPE html>
<html>
<body>
    <h2>Support Ticket Created</h2>
    <p>Hello {{submitter_name}},</p>
    <p>Your ticket <strong>{{ticket_number}}</strong> has been created.</p>
    <p><strong>Subject:</strong> {{subject}}</p>
    <p>We'll get back to you soon!</p>
</body>
</html>
```

## 🔔 **In-App Notifications**

### Notification Types:
- `ticket_created` - New ticket notifications
- `ticket_status_changed` - Status update notifications
- `ticket_assigned` - Assignment notifications
- `ticket_resolved` - Resolution notifications

### Accessing Notifications:
```typescript
// Get user notifications (implement in your API)
const notifications = await db.NotificationMeta.findAll({
  where: {
    user_id: JSON.stringify([userId]),
    notification_status: 'active'
  },
  order: [['created_at', 'DESC']]
});
```

## 🚀 **Testing**

### 1. **Manual Testing**
```bash
# Run the test script
node test-notifications.js
```

### 2. **API Testing**
```bash
# Create a ticket and check for notifications
curl -X POST http://localhost:3000/api/v1/tickets \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "ticket_title": "Test Notification",
    "ticket_description": "Testing the notification system",
    "ticket_priority": "medium",
    "ticket_type": "technical_issue",
    "ticket_module": "other"
  }'
```

### 3. **Verify Email Delivery**
- Check your email inbox for notification emails
- Monitor SMTP logs for delivery status
- Check RabbitMQ management interface for queue activity

## 🐛 **Troubleshooting**

### Common Issues:

#### 1. **Emails Not Sending**
```bash
# Check SMTP configuration
echo $SMTP_HOST $SMTP_PORT $SMTP_USER

# Verify RabbitMQ is running
rabbitmq-diagnostics status

# Check email queue
rabbitmqctl list_queues name messages
```

#### 2. **In-App Notifications Not Working**
```sql
-- Check if NotificationMeta table exists
DESCRIBE nv_notification_meta;

-- Check recent notifications
SELECT * FROM nv_notification_meta ORDER BY created_at DESC LIMIT 10;
```

#### 3. **User Information Missing**
```sql
-- Verify user data
SELECT id, user_email, user_first_name, user_last_name 
FROM nv_users WHERE id = YOUR_USER_ID;
```

### Debug Logs:
```bash
# Enable debug logging
DEBUG=notification:* npm start

# Check application logs
tail -f logs/app.log | grep notification
```

## 📊 **Monitoring**

### Key Metrics to Monitor:
1. **Email Delivery Rate** - Success vs failure ratio
2. **Queue Processing Time** - RabbitMQ message processing speed
3. **Notification Response Rate** - User engagement with notifications
4. **Error Rate** - Failed notification attempts

### Monitoring Tools:
- **RabbitMQ Management UI**: `http://localhost:15672`
- **Application Logs**: Check for notification success/failure messages
- **Database Queries**: Monitor notification table growth

## 🔮 **Advanced Usage**

### Custom Notification Functions:
```typescript
import { createInAppNotification } from '../services/notification.service';

// Send custom notification
await createInAppNotification({
  notification_content: 'Custom message here',
  notification_subject: 'Custom Subject',
  user_ids: [userId],
  notification_type: 'custom_type',
  from_user_id: adminUserId,
  redirection_type: 'custom_page',
  redirection_id: recordId
});
```

### Bulk Notifications:
```typescript
// Send to multiple users
await createInAppNotification({
  notification_content: 'System maintenance scheduled',
  notification_subject: 'Maintenance Notice',
  user_ids: [1, 2, 3, 4, 5], // Multiple users
  notification_type: 'system_announcement',
  from_user_id: systemUserId
});
```

### Role-Based Notifications:
```typescript
// Send to all admins
await createInAppNotification({
  notification_content: 'New ticket requires attention',
  notification_subject: 'Admin Alert',
  role_ids: [1, 2], // Admin role IDs
  notification_type: 'admin_alert',
  from_user_id: systemUserId
});
```

## 📝 **Best Practices**

1. **Error Handling**: Always wrap notification calls in try-catch
2. **Performance**: Don't block main operations for notifications
3. **User Experience**: Keep notification messages clear and actionable
4. **Privacy**: Only send notifications to authorized users
5. **Rate Limiting**: Implement notification frequency limits
6. **Testing**: Test notifications in staging before production
7. **Monitoring**: Set up alerts for notification system failures

## 🆘 **Support**

If you encounter issues:
1. Check the logs for error messages
2. Verify all environment variables are set
3. Ensure RabbitMQ and database are running
4. Test with the provided test script
5. Review the implementation documentation

For additional help, refer to:
- `NOTIFICATION_IMPLEMENTATION.md` - Technical implementation details
- Application logs in `logs/` directory
- RabbitMQ management interface for queue monitoring
