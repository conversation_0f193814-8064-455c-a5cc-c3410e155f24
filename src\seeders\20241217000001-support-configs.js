'use strict';

const bcrypt = require('bcrypt');

module.exports = {
  async up(queryInterface, Sequelize) {
    const hashedPin1 = await bcrypt.hash('1234', 10);
    const hashedPin2 = await bcrypt.hash('support123', 10);

    await queryInterface.bulkInsert('mo_support_configs', [
      {
        organization_id: 'org_001',
        support_pin: hashedPin1,
        is_active: true,
        allow_attachments: true,
        max_attachment_size: 5242880, // 5MB
        allowed_file_types: JSON.stringify(['pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx']),
        auto_assignment_enabled: false,
        created_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        organization_id: 'org_002',
        support_pin: hashedPin2,
        is_active: true,
        allow_attachments: true,
        max_attachment_size: 10485760, // 10MB
        allowed_file_types: JSON.stringify(['pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'xlsx']),
        auto_assignment_enabled: true,
        created_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        organization_id: 'org_003',
        support_pin: null, // No PIN required
        is_active: true,
        allow_attachments: false,
        max_attachment_size: 0,
        allowed_file_types: JSON.stringify([]),
        auto_assignment_enabled: false,
        created_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        organization_id: 'org_004',
        support_pin: null,
        is_active: false, // Support disabled
        allow_attachments: true,
        max_attachment_size: 5242880,
        allowed_file_types: JSON.stringify(['pdf', 'png', 'jpg']),
        auto_assignment_enabled: false,
        created_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('mo_support_configs', null, {});
  }
};
