import { DataTypes, Model } from "sequelize";
import { sequelize } from "./index";

// Define attachment type enum
export enum AttachmentType {
  DOCUMENT = "document",
  IMAGE = "image",
  VIDEO = "video",
  AUDIO = "audio",
  OTHER = "other"
}

// Define the attributes interface
interface TicketAttachmentAttributes {
  id?: number;
  ticket_id: number;
  item_id: number; // Reference to nv_items table
  attachment_type: AttachmentType;
  attachment_name?: string;
  file_size?: number;
  mime_type?: string;
  created_at?: Date;
  updated_at?: Date;
  created_by: number;
}

// Define the model class
class TicketAttachment extends Model<TicketAttachmentAttributes>
  implements TicketAttachmentAttributes {

  public id!: number;
  public ticket_id!: number;
  public item_id!: number;
  public attachment_type!: AttachmentType;
  public attachment_name?: string;
  public file_size?: number;
  public mime_type?: string;
  public created_at!: Date;
  public updated_at!: Date;
  public created_by!: number;

  // Timestamps (Sequelize auto-generated)
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Model associations
  static associate(models: any) {
    // TicketAttachment belongs to Ticket
    TicketAttachment.belongsTo(models.Ticket, {
      foreignKey: "ticket_id",
      as: "ticket",
    });

    // TicketAttachment belongs to Item (file storage)
    TicketAttachment.belongsTo(models.Item, {
      foreignKey: "item_id",
      as: "item",
    });

    // Soft reference to nv_users table (no Sequelize association):
    // - created_by references nv_users.id (user who uploaded the attachment)
    // User data will be fetched via raw queries when needed
  }
}

// Initialize the model
TicketAttachment.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: "Primary key for ticket attachment",
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Reference to mo_support_tickets table",
      references: {
        model: "mo_support_tickets",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    item_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Reference to nv_items table for file storage",
      references: {
        model: "nv_items",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    attachment_type: {
      type: DataTypes.ENUM(...Object.values(AttachmentType)),
      allowNull: false,
      defaultValue: AttachmentType.DOCUMENT,
      comment: "Type of attachment file",
    },
    attachment_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "Original filename of the attachment",
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "File size in bytes",
    },
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "MIME type of the file",
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: "Record creation timestamp",
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: "Record last update timestamp",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "User ID who uploaded the attachment",
      references: {
        model: "nv_users",
        key: "id",
      },
    },
  },
  {
    sequelize,
    tableName: "mo_support_ticket_attachments",
    modelName: "TicketAttachment",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        name: "idx_ticket_attachments_ticket_id",
        fields: ["ticket_id"],
      },
      {
        name: "idx_ticket_attachments_item_id",
        fields: ["item_id"],
      },
      {
        name: "idx_ticket_attachments_created_by",
        fields: ["created_by"],
      },
    ],
    comment: "Stores file attachments for support tickets using nv_items table",
  }
);

export default TicketAttachment;
export { TicketAttachmentAttributes };
