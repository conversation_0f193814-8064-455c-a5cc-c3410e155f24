# Support Ticket Enum Values Reference

## ⚠️ IMPORTANT: Updated Enum Values

The support ticket system has been updated with new enum values. **Frontend applications must use the new values listed below.**

## 🎫 Ticket Types (ticket_type)

### ✅ NEW VALUES (Use These):
```
bug_report
feature_request
general_inquiry
export_help
technical_issue
non_technical_issue
account_issue
billing
performance
notifications
support
```

### ❌ OLD VALUES (Don't Use):
```
bug
request_for_feature
general_query
technical
non_technical
```

## 📋 Complete Enum Reference

### Ticket Priorities (ticket_priority)
```
low
medium
high
urgent
```

### Ticket Statuses (ticket_status)
```
open
assigned
in_progress
on_hold
escalated
qa_review
under_review
resolved
closed
```

### Ticket Modules (ticket_module)
```
hrms
pms
other
```

## 🔄 Migration Mapping

If you need to migrate from old to new values:

| Old Value | New Value |
|-----------|-----------|
| `bug` | `bug_report` |
| `request_for_feature` | `feature_request` |
| `general_query` | `general_inquiry` |
| `technical` | `technical_issue` |
| `non_technical` | `non_technical_issue` |
| `export_help` | `export_help` (unchanged) |
| `support` | `support` (unchanged) |

## 📝 Example API Request

```json
{
  "ticket_title": "Unable to export recipe data",
  "ticket_description": "The export button is not working",
  "ticket_module": "hrms",
  "ticket_type": "bug_report",
  "ticket_priority": "high",
  "support_pin": "your-support-pin"
}
```

## 🚨 Error Resolution

If you see this error:
```
"ticket_type must be one of [bug_report, feature_request, general_inquiry, ...]"
```

**Solution:** Update your frontend to use the new enum values listed above.
