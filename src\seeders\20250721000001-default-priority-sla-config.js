'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create default configuration with priority-specific SLA times
    await queryInterface.bulkInsert('mo_support_configs', [
      {
        organization_id: 'DEFAULT',
        support_pin: null, // Default configs don't have PINs
        is_active: true,
        allow_attachments: true,
        max_attachment_size: 5242880, // 5MB
        allowed_file_types: JSON.stringify(['pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx']),
        auto_assignment_enabled: false,
        sla_response_time_hours: 24,
        sla_resolution_time_hours: 72,
        // Priority-specific SLA times (in hours)
        sla_low_priority_hours: 120,    // 5 days
        sla_medium_priority_hours: 72,  // 3 days
        sla_high_priority_hours: 48,    // 2 days
        sla_urgent_priority_hours: 24,  // 1 day
        created_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Update existing organization configs to include priority-specific SLA times
    // This will set default values for existing organizations
    await queryInterface.sequelize.query(`
      UPDATE mo_support_configs 
      SET 
        sla_low_priority_hours = 120,
        sla_medium_priority_hours = 72,
        sla_high_priority_hours = 48,
        sla_urgent_priority_hours = 24,
        updated_at = NOW()
      WHERE organization_id != 'DEFAULT'
        AND (sla_low_priority_hours IS NULL 
             OR sla_medium_priority_hours IS NULL 
             OR sla_high_priority_hours IS NULL 
             OR sla_urgent_priority_hours IS NULL)
    `);
  },

  async down(queryInterface, Sequelize) {
    // Remove the default configuration
    await queryInterface.bulkDelete('mo_support_configs', {
      organization_id: 'DEFAULT'
    }, {});

    // Reset priority-specific SLA times to NULL for existing configs
    await queryInterface.sequelize.query(`
      UPDATE mo_support_configs 
      SET 
        sla_low_priority_hours = NULL,
        sla_medium_priority_hours = NULL,
        sla_high_priority_hours = NULL,
        sla_urgent_priority_hours = NULL,
        updated_at = NOW()
      WHERE organization_id != 'DEFAULT'
    `);
  }
};
