// Test script to verify avatar URL functionality
const { getFollowersDetails } = require('./src/utils/common');

// Mock global config
global.config = {
  API_BASE_URL: 'https://staging.namastevillage.theeasyaccess.com'
};

// Test function
async function testAvatarUrls() {
  try {
    console.log('Testing avatar URL functionality...');
    
    // Test with sample follower IDs (replace with actual IDs from your database)
    const testFollowerIds = [1, 2, 3]; // Replace with real user IDs
    
    const followersDetails = await getFollowersDetails(testFollowerIds);
    
    console.log('Followers details:', JSON.stringify(followersDetails, null, 2));
    
    // Check if avatar URLs are properly formatted
    followersDetails.forEach(follower => {
      console.log(`User ${follower.id}:`);
      console.log(`  Name: ${follower.name}`);
      console.log(`  Avatar URL: ${follower.avatar_url}`);
      console.log(`  Has Avatar: ${follower.avatar_url ? 'Yes' : 'No'}`);
      console.log('---');
    });
    
  } catch (error) {
    console.error('Error testing avatar URLs:', error);
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testAvatarUrls();
}

module.exports = { testAvatarUrls };
