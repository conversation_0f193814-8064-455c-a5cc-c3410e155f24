"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add new fields to mo_support_configs table with error handling
    const tableName = "mo_support_configs";

    try {
      await queryInterface.addColumn(tableName, "sla_enabled", {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Enable/disable SLA calculation for this organization",
      });
    } catch (error) {
      console.log("sla_enabled column may already exist:", error.message);
    }

    try {
      await queryInterface.addColumn(tableName, "enable_time_tracking", {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Enable time tracking for tickets",
      });
    } catch (error) {
      console.log(
        "enable_time_tracking column may already exist:",
        error.message
      );
    }

    try {
      await queryInterface.addColumn(tableName, "allow_agent_time_updates", {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Allow agents to update time estimates and actual hours",
      });
    } catch (error) {
      console.log(
        "allow_agent_time_updates column may already exist:",
        error.message
      );
    }

    // Add time tracking fields to mo_support_tickets with error handling
    const ticketTableName = "mo_support_tickets";

    try {
      await queryInterface.addColumn(ticketTableName, "estimated_hours", {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
        comment: "Estimated hours to complete the ticket",
      });
    } catch (error) {
      console.log("estimated_hours column may already exist:", error.message);
    }

    try {
      await queryInterface.addColumn(ticketTableName, "actual_hours", {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
        comment: "Actual hours spent on the ticket",
      });
    } catch (error) {
      console.log("actual_hours column may already exist:", error.message);
    }

    try {
      await queryInterface.addColumn(ticketTableName, "work_started_at", {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "When work actually started on the ticket",
      });
    } catch (error) {
      console.log("work_started_at column may already exist:", error.message);
    }

    try {
      await queryInterface.addColumn(ticketTableName, "work_completed_at", {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "When work was completed on the ticket",
      });
    } catch (error) {
      console.log("work_completed_at column may already exist:", error.message);
    }

    try {
      await queryInterface.addColumn(
        ticketTableName,
        "time_logged_by_user_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "nv_users",
            key: "id",
          },
          comment: "User who logged the time tracking information",
        }
      );
    } catch (error) {
      console.log(
        "time_logged_by_user_id column may already exist:",
        error.message
      );
    }
  },

  async down(queryInterface) {
    // Remove fields from mo_support_configs (note: plural)
    try {
      await queryInterface.removeColumn("mo_support_configs", "sla_enabled");
    } catch (error) {
      console.log("Error removing sla_enabled:", error.message);
    }

    try {
      await queryInterface.removeColumn(
        "mo_support_configs",
        "enable_time_tracking"
      );
    } catch (error) {
      console.log("Error removing enable_time_tracking:", error.message);
    }

    try {
      await queryInterface.removeColumn(
        "mo_support_configs",
        "allow_agent_time_updates"
      );
    } catch (error) {
      console.log("Error removing allow_agent_time_updates:", error.message);
    }

    // Remove fields from mo_support_tickets
    try {
      await queryInterface.removeColumn(
        "mo_support_tickets",
        "estimated_hours"
      );
    } catch (error) {
      console.log("Error removing estimated_hours:", error.message);
    }

    try {
      await queryInterface.removeColumn("mo_support_tickets", "actual_hours");
    } catch (error) {
      console.log("Error removing actual_hours:", error.message);
    }

    try {
      await queryInterface.removeColumn(
        "mo_support_tickets",
        "work_started_at"
      );
    } catch (error) {
      console.log("Error removing work_started_at:", error.message);
    }

    try {
      await queryInterface.removeColumn(
        "mo_support_tickets",
        "work_completed_at"
      );
    } catch (error) {
      console.log("Error removing work_completed_at:", error.message);
    }

    try {
      await queryInterface.removeColumn(
        "mo_support_tickets",
        "time_logged_by_user_id"
      );
    } catch (error) {
      console.log("Error removing time_logged_by_user_id:", error.message);
    }
  },
};
